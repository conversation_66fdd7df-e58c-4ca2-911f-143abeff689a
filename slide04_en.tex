\documentclass{beamer}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{graphicx}
\usepackage{hyperref}

% Theme and color settings
\usetheme{default}
\usecolortheme{default}

% Title page information
\title{Software Testing \\ {[4]} Black Box Testing Techniques}
\author{<PERSON><PERSON><PERSON><PERSON>}
\institute{<EMAIL>}
\date{}

\begin{document}

% Frame 1: Title Page
\begin{frame}
  \titlepage
\end{frame}

% Frame 2: Test Process
\begin{frame}{Test Process (in case of Waterfall Model)}
    \begin{itemize}
        \item[\textrightarrow] \textbf{Development Process}
            \begin{itemize}
                \item[\textbullet] Requirements Analysis
                \item[\textbullet] Design
                \begin{itemize}
                    \item External Design
                    \item Internal Design
                \end{itemize}
                \item[\textbullet] Implementation (Programming)
                \item[\textbullet] \alert{Test}
                \item[\textbullet] Operation & Maintenance
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 3: Purpose and Content of Testing
\begin{frame}{Purpose and Content of Testing}
    \textbf{Purpose}
    \begin{itemize}
        \item To find potential errors (mistakes, bugs) that may exist in the software.
    \end{itemize}
    \vspace{1cm}
    \textbf{Content}
    \begin{itemize}
        \item Execute the software and confirm whether it behaves correctly according to the specification.
        \item Also, confirm as much as possible that no failures occur even in exceptional situations not specified in the specification.
    \end{itemize}
\end{frame}

% Frame 4: Difficulty of Testing
\begin{frame}{Difficulty of Testing}
    \begin{itemize}
        \item Designers and developers originally intended to create something that works according to the specification.
        \vspace{1cm}
        \item Whether it conforms to the specification can be checked along with the specification document (however, it is still difficult to encompass all cases).
        \vspace{1cm}
        \item For unspecified situations, it is very hard to check because they were unexpected or unnoticed in the first place.
    \end{itemize}
\end{frame}

% Frame 5: Importance of Testing
\begin{frame}{Importance of Testing: Cases that might have been preventable}
    \begin{itemize}
        \item AT\&T's network completely stopped, 7.5 million phone calls disconnected (1990)
            \begin{itemize}
                \item \textbf{Cause:} Just one line of code was added during an update.
            \end{itemize}
        \item Ariane 5 rocket exploded in mid-air (1996)
            \begin{itemize}
                \item \textbf{Cause:} A 64-bit number was handled as a 16-bit number.
                \item ※ Development cost over \$8 billion (nearly 1 trillion yen).
            \end{itemize}
        \item Mars explorer crashed on the Martian surface (1999)
            \begin{itemize}
                \item \textbf{Cause:} For the landing engine's thrust, the analysis and operational teams used different units: pounds and newtons.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 6: Terminology
\begin{frame}{Terminology: Test Case, Test Suite, Testing Domain}
    \begin{itemize}
        \item \textbf{Test Case}
            \begin{itemize}
                \item A specific set of input data/conditions (and the expected output).
            \end{itemize}
        \item \textbf{Test Suite}
            \begin{itemize}
                \item A collection of test cases for executing a series of tests.
            \end{itemize}
        \item \textbf{Testing Domain}
            \begin{itemize}
                \item The entire set of test cases that can assure a program is free of errors.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 7: Test Example (1): Length Conversion
\begin{frame}{Test Example (1): Length Conversion}
    \textbf{"Kilometer \textrightarrow Mile" Conversion Program} \hfill \footnotesize{※1 kilometer = 0.621371 miles}
    \vspace{0.5cm}
    \textbf{Input:} A real number from 0 up to (but not including) 10000. Valid up to 2 decimal places (digits beyond this are ignored).
    \vspace{0.5cm}
    \textbf{Output:} The value converted to miles, truncated to 3 decimal places.
    \vspace{0.5cm}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{tabular}{|l|l|}
                \hline
                \textbf{Input} & \textbf{Expected Output} \\ \hline
                0 & 0.000 \\ \hline
                9999.99 & 6213.703 \\ \hline
                1 & 0.621 \\ \hline
                1.001 & 0.621 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{tabular}{|l|l|}
                \hline
                \textbf{Input} & \textbf{Expected Output} \\ \hline
                9.99 & 6.207 \\ \hline
                10 & 6.213 \\ \hline
                -1 & Error \\ \hline
                10000 & Error \\ \hline
            \end{tabular}
        \end{column}
    \end{columns}
    \begin{flushright}
        \small It's also good to consider exceptional cases (e.g., non-numeric input).
    \end{flushright}
\end{frame}

% Frame 8: Test Example (2): Triangle Problem
\begin{frame}{Test Example (2): Triangle Problem (\textit{※A famous problem})}
    Consider a triangle formed by three integers as input, representing the lengths of its sides. Let's create test cases for a program that determines if it is:
    \begin{itemize}
        \item An isosceles triangle
        \item An equilateral triangle
        \item A scalene triangle
    \end{itemize}
\end{frame}

% Frame 9: Test Case Examples for the Triangle Problem
\begin{frame}{Test Case Examples for the Triangle Problem}
    \begin{tabular}{|l|l|}
        \hline
        \textbf{Input} & \textbf{Expected Output} \\ \hline
        (2, 5, 5) & Isosceles triangle \\ \hline
        (5, 5, 5) & Equilateral triangle \\ \hline
        (3, 4, 5) & Scalene triangle \\ \hline
        (0, 0, 0) & Error ※Becomes a point \\ \hline
        (3, 4, 7) & Error ※Becomes a line segment \\ \hline
        (2, 5, 8) & Error ※Longest side > sum of other sides \\ \hline
        (1.3, 4, 5) & Error ※Contains a real number (inequality holds) \\ \hline
        (-2, 4, 5) & Error ※Contains a negative number \\ \hline
    \end{tabular}
    \vspace{0.5cm}
    \small \textit{※Triangle inequality: The length of the longest side must be less than the sum of the lengths of the other two sides.}
    \newline
    \small It's also good to consider cases where the input order of numbers is changed.
\end{frame}

% Frame 10: Exercise 1
\begin{frame}{Exercise 1}
    \textbf{Test a program that sorts integers.}
    \vspace{0.5cm}
    You are given a program that sorts N integers.
    \begin{itemize}
        \item 0 ≤ N ≤ 10000
        \item The input is from a file (where a sequence of numbers is stored).
    \end{itemize}
    \vspace{0.5cm}
    Think of test cases for this program (here, just the input is fine).
\end{frame}

% Frame 11: What's Important in Testing
\begin{frame}{What's Important in Testing}
    \textbf{Record the results}
    \begin{itemize}
        \item Which test case found what kind of bug, who found it, and when.
        \item This becomes important information for later fixes.
    \end{itemize}
    \vspace{1cm}
    \textbf{Communicate appropriately}
    \begin{itemize}
        \item Vague information is not helpful. \hfill \textit{\small A report saying just "it doesn't work" is meaningless.}
        \item Reporting bugs is important, but the way you communicate is also important.
        \item You shouldn't present bug findings to developers in a proud tone.
        \item There is effort that only the person who made it knows, so human relationships are also important.
    \end{itemize}
\end{frame}

% Frame 12: Testability
\begin{frame}{Testability}
    Testability (ease of testing) is
    \begin{itemize}
        \item A crucial characteristic directly linked to
        \begin{itemize}
            \item Reduction in cost (man-hours) spent on testing
            \item Shortening of the testing period
        \end{itemize}
        \item Naturally leads to an improvement in quality.
    \end{itemize}
\end{frame}

% Frame 13: Improving Testability
\begin{frame}{Improving Testability}
    \textbf{Consideration in design and implementation is essential}
    \begin{itemize}
        \item It's important to make it clear which function corresponds to which part of the program.
        \item Design with testing in mind.
            \begin{itemize}
                \item Example: By dividing the system into functions (subroutines) well, they can be tested independently.
            \end{itemize}
    \end{itemize}
    \vspace{1cm}
    \textbf{Keep testing in mind when creating}
    \begin{itemize}
        \item Make it easy to understand what the program is doing so that if a bug is found during testing, it can be dealt with immediately.
        \item Creating things ad hoc (with a "just make it work for now" attitude) is, frankly, a waste of time and effort.
    \end{itemize}
\end{frame}

% Frame 14: V-Model Correspondence
\begin{frame}{V-Model Correspondence}
    \begin{itemize}
        \item \textbf{Unit/Integration Test:} Module operation confirmation
        \item \textbf{System Test and beyond:} System operation confirmation
    \end{itemize}
    \begin{center}
        \includegraphics[width=0.8\textwidth]{v-model.png}
    \end{center}
    \textit{Note: The image "v-model.png" shows the V-model of software development. On the left side, from top to bottom, are "Requirements Analysis", "External Design", "Internal Design", and "Implementation (Programming)". On the right side, from bottom to top, are "Unit/Integration Test", "System Test", and "Acceptance/Operational Test". Arrows show the correspondence between the left and right sides.}
\end{frame}

% Frame 15: Module
\begin{frame}{Module}
    A module is a separable unit of a program.
    \begin{itemize}
        \item It refers to a chunk of a program (software component) that can be replaced at that level.
        \item In C, this corresponds to a "function" or "a collection of several functions (usually one program)".
        \item In object-oriented languages like Java, a "class" corresponds to this (※ A "method" is equivalent to a "function", but a method cannot exist by itself).
    \end{itemize}
\end{frame}

% Frame 16: Unit Test, Integration Test
\begin{frame}{Unit Test, Integration Test}
    \textbf{Unit Test}
    \begin{itemize}
        \item For a single module, various inputs are given to check if appropriate/correct outputs are obtained.
    \end{itemize}
    \vspace{1cm}
    \textbf{Integration Test}
    \begin{itemize}
        \item Multiple modules (which have passed unit tests) are combined to check if the modules can perform appropriate I/O in a state with coupling relationships.
    \end{itemize}
\end{frame}

% Frame 17: System Test, Acceptance/Operational Test
\begin{frame}{System Test, Acceptance/Operational Test}
    \textbf{System Test}
    \begin{itemize}
        \item To confirm whether the system operates according to the specification.
    \end{itemize}
    \vspace{1cm}
    \textbf{Acceptance/Operational Test}
    \begin{itemize}
        \item Similar to a system test, but it is a test under the actual operational environment, meaning a test by the customers/users.
        \item Actually having them use it (e.g., for work).
    \end{itemize}
\end{frame}

% Frame 18: Classification of Tests
\begin{frame}{Classification of Tests}
    \begin{itemize}
        \item \textbf{Black Box Testing}
            \begin{itemize}
                \item The program's internals are not looked at (treated as a black box), and an operational test is performed based on the specification.
            \end{itemize}
        \item \textbf{White Box Testing}
            \begin{itemize}
                \item An operational test is performed based on the internal structure of the program (mainly flowcharts).
            \end{itemize}
        \item \textbf{Random Testing}
            \begin{itemize}
                \item Test cases are created randomly (at random) to perform an operational test.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 19: Black Box Testing Technique (1)
\begin{frame}{Black Box Testing Technique (1): Equivalence Partitioning}
    To design test cases efficiently, the input space is divided based on a type of equivalence relation.
    \begin{itemize}
        \item One partition is called an "equivalence class".
        \item An equivalence class is a collection of inputs that are expected to be treated the same by the program for each input condition.
        \item (There are two types of equivalence classes)
            \begin{itemize}
                \item \textbf{Valid Equivalence Class:} Things that are valid as input.
                \item \textbf{Invalid Equivalence Class:} Things that are invalid as input.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 20: Equivalence Relation
\begin{frame}{Equivalence Relation}
    A relation (denoted by R) between elements of a set that satisfies all of the following properties:
    \begin{itemize}
        \item \textbf{Reflexivity:} xRx
        \item \textbf{Symmetry:} if xRy then yRx
        \item \textbf{Transitivity:} if xRy and yRz then xRz
    \end{itemize}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \textbf{Examples of R that are equivalence relations:}
            \begin{itemize}
                \item = (equal)
                \item || (lines are parallel)
                \item "remainder when divided by 10 is the same"
            \end{itemize}
        \end{column}
        \begin{column}{0.5\textwidth}
            \textbf{Examples of R that are NOT equivalence relations:}
            \begin{itemize}
                \item < (less than)
                \item "(shapes) overlap"
                \item "(x) knows (y)"
            \end{itemize}
        \end{column}
    \end{columns}
    \vspace{0.5cm}
    \textit{If you decide on one representative example, you can form a group (equivalence class) of things that are in an equivalence relation with it.}
\end{frame}

% Frame 21: Break
\begin{frame}
    \begin{center}
        \Huge 10-minutes rest break
    \end{center}
\end{frame}

% Frame 22: Equivalence Partitioning Example 1 (1/2)
\begin{frame}{Equivalence Partitioning Example 1 (1/2)}
    \textbf{Input:} Subject grade (integer value)
    \newline
    \textbf{Output:} One of "S, A, B, C, D, NA"
    \vspace{1cm}
    \textbf{Specification:}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{itemize}
                \item 90\textasciitilde100 \textrightarrow S
                \item 70\textasciitilde79 \textrightarrow B
                \item 0\textasciitilde59 \textrightarrow D
            \end{itemize}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{itemize}
                \item 80\textasciitilde89 \textrightarrow A
                \item 60\textasciitilde69 \textrightarrow C
                \item -1 \textrightarrow NA
            \end{itemize}
        \end{column}
    \end{columns}
\end{frame}

% Frame 23: Equivalence Partitioning Example 1 (2/2)
\begin{frame}{Equivalence Partitioning Example 1 (2/2)}
    The valid and invalid equivalence classes are as follows:
    \begin{center}
        \begin{tabular}{|c|c|}
            \hline
            \textbf{Valid Equivalence Class} & \textbf{Invalid Equivalence Class} \\ \hline
            90 \textasciitilde 100 & 101 or more \\ \hline
            80 \textasciitilde 89 & -2 or less \\ \hline
            70 \textasciitilde 79 & \\ \hline
            60 \textasciitilde 69 & \\ \hline
            0 \textasciitilde 59 & \\ \hline
            -1 & \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \textit{Select a value from each equivalence class to perform the test.}
\end{frame}

% Frame 24: Equivalence Partitioning Example 2 (1/4)
\begin{frame}{Equivalence Partitioning Example 2 (1/4)}
    \textbf{Parking Fee Calculation Program}
    \newline
    \textbf{Input (1): Parking time (unit: minutes)}
    \begin{itemize}
        \item 1 min \textasciitilde 30 min = 100 yen
        \item 31 min \textasciitilde 60 min = 200 yen
        \item After that = 100 yen up per hour
    \end{itemize}
    \textbf{Input (2): Purchase amount at a partner shop}
    \begin{itemize}
        \item 2,000 yen or more for 1 hour free
    \end{itemize}
    \small\textit{※If parking time exceeds 1 hour, only 200 yen (for the first hour) is discounted.}
\end{frame}

% Frame 25: Equivalence Partitioning Example 2 (2/4)
\begin{frame}{Equivalence Partitioning Example 2 (2/4)}
    \begin{center}
        \begin{tabular}{|l|c|c|}
            \hline
            \textbf{Input Condition} & \textbf{Valid Equivalence Class} & \textbf{Invalid Equivalence Class} \\ \hline
            (1) Parking Time & 1\textasciitilde30 & 0 or less \\ \cline{2-2}
             & 31\textasciitilde60 & \\ \cline{2-2}
             & 61 or more & \\ \hline
            (2) Purchase Amount & 0\textasciitilde1999 & -1 or less \\ \cline{2-2}
             & 2000 or more & \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \small\textit{※Negative values for time or amount are not possible, but here we assume they can be "entered".}
    \newline
    \small\textit{Classes are grouped together because the calculation method is the same.}
\end{frame}

% Frame 26: Equivalence Partitioning Example 2 (3/4)
\begin{frame}{Equivalence Partitioning Example 2 (3/4)}
    First, for the two input conditions, create combinations of valid equivalence classes.
    \begin{center}
        \includegraphics[width=\textwidth]{table1.png}
    \end{center}
    \textit{Note: The image "table1.png" shows a table for creating test cases. The rows represent conditions for "Parking Time" (Valid: 1-30, 31-60, 61-; Invalid: ~0) and "Purchase Amount" (Valid: 0-1999, 2000-; Invalid: minus). The columns are combinations of these. Circles indicate which combinations to test. A vertical column of circles represents one test case. An example is given: 70 min parking, 2100 yen purchase -> Parking fee 100 yen (= 300 - 200).}
\end{frame}

% Frame 27: Equivalence Partitioning Example 2 (4/4)
\begin{frame}{Equivalence Partitioning Example 2 (4/4)}
    Next, create combinations that include only one invalid equivalence class.
    \begin{center}
        \includegraphics[width=\textwidth]{table2.png}
    \end{center}
    \textit{Note: The image "table2.png" is similar to the previous one, but this time it shows combinations where one of the conditions is invalid. An example is given: 0 min parking, 2100 yen purchase -> Display an error for invalid input.}
\end{frame}

% Frame 28: Procedure for Equivalence Partitioning
\begin{frame}{Procedure for Equivalence Partitioning}
    \begin{enumerate}
        \item Set valid and invalid equivalence classes for each input condition.
        \item Create all combinations of valid equivalence classes.
        \item Create combinations that include only one invalid equivalence class.
    \end{enumerate}
    \vspace{0.5cm}
    \small\textit{※Of course, you can add tests with two or more invalid classes, but it's better to first check that no issues arise when mixing in just one invalid class.}
\end{frame}

% Frame 29: Black Box Testing Method (2)
\begin{frame}{Black Box Testing Method (2): Boundary-Value Analysis}
    Focus on the "change points" (boundaries) of the program's conditions.
    \begin{itemize}
        \item Test at the boundaries of input and output conditions.
        \item Test at the edges or boundaries of valid and invalid equivalence classes, or just outside them.
    \end{itemize}
    \begin{center}
        \includegraphics[width=0.8\textwidth]{boundary.png}
    \end{center}
    \textit{Note: The image "boundary.png" illustrates testing points around a boundary. It shows an input value line with sections for "Invalid", "Valid", and "Invalid". Arrows point to the exact boundaries and the points just inside and outside the valid range, indicating these are the points to test.}
\end{frame}

% Frame 30: Why focus on condition boundaries?
\begin{frame}{Why focus on condition boundaries?}
    It's surprisingly common to make mistakes in the condition-checking parts of a program.
    \vspace{0.5cm}
    (Example) A mistake in the conditional expression of an if statement in a grade evaluation program:
    \begin{verbatim}
    if (score > 90) {
        printf("S\n");
    }
    \end{verbatim}
    \begin{itemize}
        \item \textit{A score of 90 does not result in "S".}
        \item \textit{A score of 101 results in "S" instead of an "error".}
    \end{itemize}
    \vspace{0.5cm}
    Correctly:
    \begin{verbatim}
    if (score >= 90 && score <= 100) {
        printf("S\n");
    }
    \end{verbatim}
\end{frame}

% Frame 31: Boundary-Value Analysis Example
\begin{frame}{Boundary-Value Analysis Example}
    Consider a program that outputs a decimal number (0 to less than 16) in binary.
    \begin{center}
        \begin{tabular}{|c|c|c|l|}
            \hline
            \multicolumn{2}{|c|}{\textbf{Condition Boundary}} & \multicolumn{2}{c|}{\textbf{Edge of Equivalence Class}} \\
            \multicolumn{2}{|c|}{\textbf{(focus on output bits)}} & \multicolumn{2}{c|}{\textbf{and its outside}} \\ \hline
            (Input) & & & \\ \hline
            0, 1 & 1-bit & 0 & Valid minimum value \\ \hline
            2, 3 & 2-bit & 15 & Valid maximum value \\ \hline
            4, 7 & 3-bit & -1 & Invalid \\ \hline
            8, 15 & 4-bit & 16 & Invalid \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \textit{It's also good to have other invalid equivalence classes, like non-integer inputs.}
\end{frame}

% Frame 32: Exercise 2
\begin{frame}{Exercise 2}
    \textbf{Design test cases using boundary-value analysis.}
    \vspace{0.5cm}
    \textbf{Test Target:} A program that takes two integers (0 to less than 10) as input and outputs their sum in hexadecimal.
    \begin{itemize}
        \item Output is 1 digit if the sum is 0-15, 2 digits if 16-18.
        \item For invalid input, display ERROR.
    \end{itemize}
\end{frame}

% Frame 33: (Reference: Other Methods) Using Orthogonal Arrays
\begin{frame}{(Reference: Other Methods) Using Orthogonal Arrays}
    When there are several functions to be tested, it is also important to cover their combinations.
    \begin{itemize}
        \item Simply choosing 2 out of n functions results in n(n-1)/2 combinations, and the number of combinations can become enormous.
        \item For example, for n=10 it's 45 combinations, for n=20 it's 190, for n=30 it's 435.
    \end{itemize}
    \vspace{0.5cm}
    \textbf{Orthogonal arrays can be used to create combinations efficiently.}
\end{frame}

% Frame 34: Orthogonal Array
\begin{frame}{Orthogonal Array}
    For example, in a word processor's character decoration:
    \newline
    A = Bold, B = Italic, C = Underline, D = Shaded
    \begin{columns}
        \begin{column}{0.4\textwidth}
            \textbf{Covering 2 functions (A,B)}
            \begin{tabular}{|c|c|}
                \hline
                A & B \\ \hline
                0 & 0 \\ \hline
                0 & 1 \\ \hline
                1 & 0 \\ \hline
                1 & 1 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{0.6\textwidth}
            \textbf{For 4 functions (A,B,C,D), the 8 combinations on the right are OK.}
            \begin{tabular}{|c|c|c|c|}
                \hline
                A & B & C & D \\ \hline
                0 & 0 & 0 & 0 \\ \hline
                0 & 0 & 0 & 1 \\ \hline
                0 & 1 & 1 & 0 \\ \hline
                0 & 1 & 1 & 1 \\ \hline
                1 & 0 & 1 & 0 \\ \hline
                1 & 0 & 1 & 1 \\ \hline
                1 & 1 & 0 & 0 \\ \hline
                1 & 1 & 0 & 1 \\ \hline
            \end{tabular}
        \end{column}
    \end{columns}
    \textit{If you take any two columns, the pairs (0,0), (0,1), (1,0), (1,1) appear the same number of times. This type of table is called an orthogonal array and is effective in combination testing.}
\end{frame}

% Frame 35: How to make an orthogonal array
\begin{frame}{How to make an orthogonal array}
    The basics are in 3 steps:
    \begin{center}
        \includegraphics[width=0.9\textwidth]{ortho1.png}
    \end{center}
    \textit{Note: The image "ortho1.png" shows a 3-step process. 1. Start with a column of 0 and 1. Duplicate each row. 2. Create a new column, alternating 0 and 1. 3. Create a new column by taking the XOR of the existing columns.}
\end{frame}

% Frame 36: How to make an orthogonal array (continued)
\begin{frame}{How to make an orthogonal array (continued)}
    (2nd round)
    \begin{center}
        \includegraphics[width=\textwidth]{ortho2.png}
    \end{center}
    \textit{Note: The image "ortho2.png" shows the continuation of the process to add more columns. The process from the previous slide is repeated to expand the array. It's mentioned that 2 more columns can be added.}
\end{frame}

% Frame 37: Exercise 3
\begin{frame}{Exercise 3}
    \textbf{Add two more columns to the orthogonal array.}
    \vspace{0.5cm}
    In the previous orthogonal array, two more columns can be added. Find them.
    \begin{center}
        \includegraphics[width=0.8\textwidth]{ortho3.png}
    \end{center}
    \textit{Note: The image "ortho3.png" shows the 8x5 orthogonal array from the previous slide and asks to find the remaining two columns.}
\end{frame}

% Frame 38: Effectiveness of 2-function combinations
\begin{frame}{Effectiveness of 2-function combinations}
    \textbf{Relationship between the number of combination functions and faults: Bug detection rate (\%)}
    \begin{center}
        \includegraphics[width=\textwidth]{fault_table.png}
    \end{center}
    \textit{Note: The image "fault_table.png" is a table showing bug detection rates for different systems (Expert system, OS, Embedded medical device, Mozilla, Apache, DB) based on the number of interacting functions (1 to 6). The data suggests that testing 2-way combinations finds a significant percentage of bugs.}
    \newline
    \tiny{D.R. Kuhn, et al., "Software fault interactions and implications for software testing," IEEE Trans. Softw. Eng., vol.30, no.6, pp.418-421, June 2004.}
\end{frame}

% Frame 39: Summary
\begin{frame}{Summary}
    \begin{itemize}
        \item Testing: The task of finding "bugs".
            \begin{itemize}
                \item It is the key to quality assurance.
                \item A flexible mindset is required.
                \item Example: Triangle problem.
            \end{itemize}
        \item Black-box testing: Checking behavior from the outside.
            \begin{itemize}
                \item \textbf{Equivalence partitioning:} Combination of valid and invalid equivalence classes.
                \item \textbf{Boundary-value analysis:} Focus on the boundaries of I/O conditions and equivalence classes.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 40: Notice
\begin{frame}{Notice}
    The next class is an exercise class where you will create and test C programs on your own PC.
    \begin{itemize}
        \item All students have to develop and test C programs on your PCs.
    \end{itemize}
    \vspace{0.5cm}
    You must finish installing a C compiler on your PC (recommendation: gcc).
\end{frame}

% Frame 41: Homework
\begin{frame}{Homework}
    \begin{center}
        \Huge Answer "[04] quiz"
        \Large (by this Friday 23:59)
    \end{center}
    \vspace{1cm}
    \textit{Note: Your quiz score will be a part of your final evaluation.}
\end{frame}

% Frame 42: Exercise 1 Answer Example
\begin{frame}{【Exercise 1】Answer Example}
    \begin{itemize}
        \item Random sequence of numbers
            \begin{itemize}
                \item \{5, 1, 9, 2, 3, 7, ...\}
            \end{itemize}
        \item Sorted sequence of numbers
            \begin{itemize}
                \item \{1, 2, 3, 4, ...\}
                \item \{100, 99, 98, 97, ...\}
            \end{itemize}
        \item Case with only 1 item, case with more than N items
            \begin{itemize}
                \item \{2\}
                \item \{1, 2, 3, ..., N, N+1\}
            \end{itemize}
        \item Case with 0 items
            \begin{itemize}
                \item \{\}
            \end{itemize}
    \end{itemize}
    \textit{Also consider exceptions like file not found, failed to open, etc.}
\end{frame}

% Frame 43: Exercise 2 Answer Example
\begin{frame}{【Exercise 2】Answer Example}
    \begin{center}
        \begin{tabular}{|c|c|c|c|}
            \hline
            \multicolumn{2}{|c|}{\textbf{Condition Boundary}} & \multicolumn{2}{c|}{\textbf{Edge of Equivalence Class and Outside}} \\ \hline
            6+9 & 1 digit & 0+0 & Valid minimum value \\ \hline
            8+7 & & 9+9 & Valid maximum value \\ \hline
            7+9 & & -1+0 & Invalid \\ \hline
            8+8 & 2 digits & 10+0 & Invalid \\ \hline
            9+7 & & 10+10 & Invalid \\ \hline
            5+5 & Symbol & -1+10 & Invalid \\ \hline
        \end{tabular}
    \end{center}
    \textit{It is also good to have other invalid equivalence classes, like non-integer inputs.}
    \newline
    \tiny{※For the 1-digit case, both 6+9 and 8+7 are considered to account for both patterns of number magnitude (not required). Same for the 2-digit case.}
\end{frame}

% Frame 44: Exercise 3 Answer
\begin{frame}{【Exercise 3】Answer}
    \textbf{Add two more columns to the orthogonal array.}
    \begin{center}
    \begin{tabular}{|c|c|c|c|c|c|c|}
        \hline
        0 & 0 & 0 & 0 & 0 & 0 & 0 \\ \hline
        0 & 0 & 0 & 1 & 1 & 1 & 1 \\ \hline
        0 & 1 & 1 & 0 & 0 & 1 & 1 \\ \hline
        0 & 1 & 1 & 1 & 1 & 0 & 0 \\ \hline
        1 & 0 & 1 & 0 & 1 & 0 & 1 \\ \hline
        1 & 0 & 1 & 1 & 0 & 1 & 0 \\ \hline
        1 & 1 & 0 & 0 & 1 & 1 & 0 \\ \hline
        1 & 1 & 0 & 1 & 0 & 0 & 1 \\ \hline
    \end{tabular}
    \end{center}
\end{frame}

\end{document}