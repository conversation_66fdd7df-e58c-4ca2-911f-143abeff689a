[{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Emergency stop.\n<*> ...sktop/SE113/Course_Materials/slide04_vi.tex",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": " ==> Fatal error occurred, no output PDF file produced!\nTranscript written on slide04_vi.log.\nLatexmk: Getting log file 'slide04_vi.log'\nLatexmk: Examining 'slide04_vi.fls'\nLatexmk: Examining 'slide04_vi.log'\nLatexmk: References changed.\nLatexmk: Summary of warnings from last run of *latex:",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character ≤ (U+2264)",
	"source": "LaTeX",
	"startLineNumber": 181,
	"startColumn": 1,
	"endLineNumber": 181,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character ≤ (U+2264)",
	"source": "LaTeX",
	"startLineNumber": 181,
	"startColumn": 1,
	"endLineNumber": 181,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `v-model.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 241,
	"startColumn": 1,
	"endLineNumber": 241,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Paragraph ended before \\beamer@parseitem was complete.\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 341,
	"startColumn": 1,
	"endLineNumber": 341,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing \\endcsname inserted.\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing \\endcsname inserted.\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Environment \\vrule  undefined.",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing number, treated as zero.\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Illegal unit of measure (pt inserted).\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n<recently read> }",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "You can't use `\\hrule' here except with leaders.\n\\frame  ... #1\\vrule \\@width \\@wholewidth }\\hrule",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing number, treated as zero.\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Illegal unit of measure (pt inserted).\n<to be read again>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing \\endgroup inserted.\n<inserted text>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing } inserted.\n<inserted text>",
	"source": "LaTeX",
	"startLineNumber": 344,
	"startColumn": 1,
	"endLineNumber": 344,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Something's wrong--perhaps a missing \\item.",
	"source": "LaTeX",
	"startLineNumber": 346,
	"startColumn": 1,
	"endLineNumber": 346,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "\\begin{frame} on input line 343 ended by \\end{center}.",
	"source": "LaTeX",
	"startLineNumber": 346,
	"startColumn": 1,
	"endLineNumber": 346,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 347,
	"startColumn": 1,
	"endLineNumber": 347,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 372,
	"startColumn": 1,
	"endLineNumber": 372,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 391,
	"startColumn": 1,
	"endLineNumber": 391,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 408,
	"startColumn": 1,
	"endLineNumber": 408,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 427,
	"startColumn": 1,
	"endLineNumber": 427,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `table1.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 433,
	"startColumn": 1,
	"endLineNumber": 433,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 436,
	"startColumn": 1,
	"endLineNumber": 436,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `table2.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 442,
	"startColumn": 1,
	"endLineNumber": 442,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 445,
	"startColumn": 1,
	"endLineNumber": 445,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 456,
	"startColumn": 1,
	"endLineNumber": 456,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `boundary.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 466,
	"startColumn": 1,
	"endLineNumber": 466,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 469,
	"startColumn": 1,
	"endLineNumber": 469,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 492,
	"startColumn": 1,
	"endLineNumber": 492,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 511,
	"startColumn": 1,
	"endLineNumber": 511,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 522,
	"startColumn": 1,
	"endLineNumber": 522,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 533,
	"startColumn": 1,
	"endLineNumber": 533,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 569,
	"startColumn": 1,
	"endLineNumber": 569,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `ortho1.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 575,
	"startColumn": 1,
	"endLineNumber": 575,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 578,
	"startColumn": 1,
	"endLineNumber": 578,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `ortho2.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 584,
	"startColumn": 1,
	"endLineNumber": 584,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 587,
	"startColumn": 1,
	"endLineNumber": 587,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `ortho3.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 595,
	"startColumn": 1,
	"endLineNumber": 595,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 598,
	"startColumn": 1,
	"endLineNumber": 598,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Package pdftex.def: File `fault_table.png' not found: using draft setting.",
	"source": "LaTeX",
	"startLineNumber": 604,
	"startColumn": 1,
	"endLineNumber": 604,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing $ inserted.\n<inserted text>",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten $.\n<recently read> \\egroup",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten $.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 609,
	"startColumn": 1,
	"endLineNumber": 609,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing $ inserted.\n<inserted text>",
	"source": "LaTeX",
	"startLineNumber": 610,
	"startColumn": 1,
	"endLineNumber": 610,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Missing } inserted.\n<inserted text>",
	"source": "LaTeX",
	"startLineNumber": 610,
	"startColumn": 1,
	"endLineNumber": 610,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 626,
	"startColumn": 1,
	"endLineNumber": 626,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 636,
	"startColumn": 1,
	"endLineNumber": 636,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 646,
	"startColumn": 1,
	"endLineNumber": 646,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 【 (U+3010)",
	"source": "LaTeX",
	"startLineNumber": 649,
	"startColumn": 1,
	"endLineNumber": 649,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 】 (U+3011)",
	"source": "LaTeX",
	"startLineNumber": 649,
	"startColumn": 1,
	"endLineNumber": 649,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 671,
	"startColumn": 1,
	"endLineNumber": 671,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 【 (U+3010)",
	"source": "LaTeX",
	"startLineNumber": 674,
	"startColumn": 1,
	"endLineNumber": 674,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 】 (U+3011)",
	"source": "LaTeX",
	"startLineNumber": 674,
	"startColumn": 1,
	"endLineNumber": 674,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 690,
	"startColumn": 1,
	"endLineNumber": 690,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 【 (U+3010)",
	"source": "LaTeX",
	"startLineNumber": 693,
	"startColumn": 1,
	"endLineNumber": 693,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Unicode character 】 (U+3011)",
	"source": "LaTeX",
	"startLineNumber": 693,
	"startColumn": 1,
	"endLineNumber": 693,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "Extra }, or forgotten \\endgroup.\n\\endframe ->\\egroup",
	"source": "LaTeX",
	"startLineNumber": 708,
	"startColumn": 1,
	"endLineNumber": 708,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "\\begin{frame} on input line 693 ended by \\end{document}.",
	"source": "LaTeX",
	"startLineNumber": 710,
	"startColumn": 1,
	"endLineNumber": 710,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "You can't use `\\end' in internal vertical mode.\n\\enddocument ...cument/end}\\deadcycles \\z@ \\@@end",
	"source": "LaTeX",
	"startLineNumber": 710,
	"startColumn": 1,
	"endLineNumber": 710,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 8,
	"message": "\\begin{frame} on input line 693 ended by \\end{document}.",
	"source": "LaTeX",
	"startLineNumber": 710,
	"startColumn": 1,
	"endLineNumber": 710,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Missing character: There is no � in font cmss10!",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Package rerunfilecheck: File `slide04_vi.out' has changed.\n(rerunfilecheck)\tRerun to get outlines right\n(rerunfilecheck)\tor use package `bookmark'.",
	"source": "LaTeX",
	"startLineNumber": 1,
	"startColumn": 1,
	"endLineNumber": 1,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX Font: Font shape `T5/cmss/m/it' in size <14.4> not available\n(Font)\tFont shape `T5/cmss/m/sl' tried instead.",
	"source": "LaTeX",
	"startLineNumber": 148,
	"startColumn": 1,
	"endLineNumber": 148,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX Font: Font shape `T5/cmss/m/it' in size <10> not available\n(Font)\tFont shape `T5/cmss/m/sl' tried instead.",
	"source": "LaTeX",
	"startLineNumber": 168,
	"startColumn": 1,
	"endLineNumber": 168,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX Font: Font shape `T5/cmss/m/it' in size <10.95> not available\n(Font)\tFont shape `T5/cmss/m/sl' tried instead.",
	"source": "LaTeX",
	"startLineNumber": 198,
	"startColumn": 1,
	"endLineNumber": 198,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `v-model.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 241,
	"startColumn": 1,
	"endLineNumber": 241,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `table1.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 433,
	"startColumn": 1,
	"endLineNumber": 433,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `table2.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 442,
	"startColumn": 1,
	"endLineNumber": 442,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `boundary.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 466,
	"startColumn": 1,
	"endLineNumber": 466,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `ortho1.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 575,
	"startColumn": 1,
	"endLineNumber": 575,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `ortho2.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 584,
	"startColumn": 1,
	"endLineNumber": 584,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `ortho3.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 595,
	"startColumn": 1,
	"endLineNumber": 595,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: File `fault_table.png' not found.",
	"source": "LaTeX",
	"startLineNumber": 604,
	"startColumn": 1,
	"endLineNumber": 604,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\` invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\~ invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\^ invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\^ invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\uhorn invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\u invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\uhorn invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\ohorn invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\dj invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\~ invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\` invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\` invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\` invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\d invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\dj invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\h invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\' invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX: Command \\~ invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX Font: Command \\tiny invalid in math mode.",
	"source": "LaTeX",
	"startLineNumber": 608,
	"startColumn": 1,
	"endLineNumber": 608,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 4,
	"message": "LaTeX Font: Font shape `T5/cmss/m/it' in size <6> not available\n(Font)\tFont shape `T5/cmss/m/sl' tried instead.",
	"source": "LaTeX",
	"startLineNumber": 608,
	"startColumn": 1,
	"endLineNumber": 608,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\vbox (166.24596pt too high)",
	"source": "LaTeX",
	"startLineNumber": 241,
	"startColumn": 1,
	"endLineNumber": 241,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (55.5148pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 350,
	"startColumn": 1,
	"endLineNumber": 350,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (55.5148pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 375,
	"startColumn": 1,
	"endLineNumber": 375,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (197.16817pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 378,
	"startColumn": 1,
	"endLineNumber": 378,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (55.5148pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 394,
	"startColumn": 1,
	"endLineNumber": 394,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (39.26892pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 411,
	"startColumn": 1,
	"endLineNumber": 411,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (278.94145pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 413,
	"startColumn": 1,
	"endLineNumber": 413,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (39.26892pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 430,
	"startColumn": 1,
	"endLineNumber": 430,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (21.90005pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 433,
	"startColumn": 1,
	"endLineNumber": 433,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (39.26892pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 439,
	"startColumn": 1,
	"endLineNumber": 439,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (21.90005pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 442,
	"startColumn": 1,
	"endLineNumber": 442,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (22.6341pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 448,
	"startColumn": 1,
	"endLineNumber": 448,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (131.9685pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 459,
	"startColumn": 1,
	"endLineNumber": 459,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (24.66138pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 472,
	"startColumn": 1,
	"endLineNumber": 472,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (67.70642pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 491,
	"startColumn": 1,
	"endLineNumber": 491,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (143.80484pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 498,
	"startColumn": 1,
	"endLineNumber": 498,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (144.82648pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 525,
	"startColumn": 1,
	"endLineNumber": 525,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (6.53462pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 575,
	"startColumn": 1,
	"endLineNumber": 575,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (16.08014pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 581,
	"startColumn": 1,
	"endLineNumber": 581,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (21.90005pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 584,
	"startColumn": 1,
	"endLineNumber": 584,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (23.13397pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 601,
	"startColumn": 1,
	"endLineNumber": 601,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (21.90005pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 604,
	"startColumn": 1,
	"endLineNumber": 604,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (720.62971pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (452.24115pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 606,
	"startColumn": 1,
	"endLineNumber": 606,
	"endColumn": 65536
},{
	"resource": "/c:/Users/<USER>/Desktop/SE113/Course_Materials/slide04_vi.tex",
	"owner": "LaTeX",
	"severity": 2,
	"message": "Overfull \\hbox (160.10641pt too wide)",
	"source": "LaTeX",
	"startLineNumber": 676,
	"startColumn": 1,
	"endLineNumber": 676,
	"endColumn": 65536
}]
[{
	"resource": "/C:/Users/<USER>/AppData/Local/Programs/MiKTeX/tex/latex/babel-vietnamese/vietnamese.ldf",
	"owner": "LaTeX",
	"severity": 4,
	"message": "Package babel: No hyphenation patterns were preloaded for\n(babel)\tthe language 'Vietnamese' into the format.\n(babel)\tPlease, configure your TeX system to add them and\n(babel)\trebuild the format. Now I will use the patterns\n(babel)\tpreloaded for \\language=0 instead.",
	"source": "LaTeX",
	"startLineNumber": 36,
	"startColumn": 1,
	"endLineNumber": 36,
	"endColumn": 65536
}]