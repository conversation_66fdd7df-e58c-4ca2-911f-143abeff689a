\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\providecommand \babel@aux [2]{\global \let \babel@toc \@gobbletwo }
\@nameuse{bbl@beforestart}
\catcode `"\active 
\babel@aux{vietnamese}{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{3}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{4}{4/4}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{5}{5/5}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {5}{5}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{6}{6/6}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {6}{6}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{7}{7/7}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {7}{7}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{8}{8/8}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {8}{8}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{9}{9/9}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {9}{9}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{10}{10/10}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {10}{10}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{11}{11/11}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {11}{11}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{12}{12/12}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {12}{12}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{13}{13/13}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {13}{13}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{14}{14/14}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {14}{14}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{15}{15/15}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {15}{15}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{16}{16/16}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {16}{16}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{17}{17/17}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {17}{17}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{18}{18/18}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {18}{18}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{19}{19/19}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {19}{19}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{19}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{19}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{19}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {19}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {20}}}
\gdef \@abspage@last{19}
