\documentclass{beamer}
\usetheme{default}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{tikz}
\usetikzlibrary{shapes, arrows}

\title{Software Testing \\ [2] Overview of Software Engineering}
\author{AMAN Hirohisa \\ \texttt{<EMAIL>}}
\date{}

\begin{document}

% Page 1
\begin{frame}
    \titlepage
\end{frame}

% Page 2
\begin{frame}{Role of Software}
    \begin{itemize}
        \item A computer system is a combination of hardware and software.
    \end{itemize}
    \vfill
    \begin{columns}[T]
        \begin{column}{.3\textwidth}
            \begin{beamercolorbox}[sep=0.5em,center]{beamercolorbox}
                \textbf{Computer System}
                \begin{itemize}
                    \item Software
                    \item Hardware
                \end{itemize}
            \end{beamercolorbox}
        \end{column}
        \begin{column}{.7\textwidth}
            \begin{block}{The role of software is to master the hardware}
                How to utilize the hardware depends on the software.
            \end{block}
        \end{column}
    \end{columns}
    \vfill
    \begin{itemize}
        \item Games are a typical example.
    \end{itemize}
\end{frame}

% Page 3
\begin{frame}{(Reference) Major Home Video Game Consoles by 2000}
    \begin{columns}
        \begin{column}{.7\textwidth}
            \tiny
            \begin{tabular}{|l|c|c|c|}
                \hline
                \textbf{Name} & \textbf{CPU} & \textbf{Main Memory} & \textbf{Year} \\ \hline
                Family Computer & 8-bit & 2KB & 1983 \\
                Super Famicom & 16-bit & 128KB & 1990 \\
                PlayStation & 32-bit & 2MB & 1994 \\
                Sega Saturn & 32-bit & 2MB & 1994 \\
                NINTENDO64 & 64-bit & 4.5MB & 1996 \\
                Dreamcast & 32-bit & 16MB & 1998 \\
                PlayStation2 & 128-bit & 32MB & 2000 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{.3\textwidth}
            \begin{beamercolorbox}[sep=0.5em,center]{beamercolorbox}
                \textbf{Software Requirements} \\
                High Quality \\
                \& High Reliability \\
                \& Low Price
            \end{beamercolorbox}
        \end{column}
    \end{columns}
    \begin{itemize}
        \item Software was engineered to draw out the hardware's potential for engaging games.
        \item On the other hand, fixing post-release defects was extremely difficult (due to distribution on cartridges or CD/DVDs). \rightarrow \textbf{Testing is crucial}.
    \end{itemize}
\end{frame}

% Page 4
\begin{frame}{Software Engineering}
    \begin{block}{A discipline for efficiently developing high-quality software}
        \begin{itemize}
            \item It is \textbf{not} an approach of "a few heroes working hard will manage somehow".
            \item It is a discipline for making development succeed.
        \end{itemize}
    \end{block}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{Theories \& Techniques for Process}
                For efficiently performing:
                \begin{itemize}
                    \item Requirements Analysis
                    \item Design
                    \item Programming
                    \item Testing, etc.
                \end{itemize}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{Theories \& Techniques for Management}
                For successfully managing development activities (Development Management).
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Page 5
\begin{frame}{A Digression... The Story of Man-Months}
    \begin{block}{Man-Month = Number of People × Number of Months}
        A unit of effort (labor) for development.
        \begin{itemize}
            \item Ex: 6 man-months = 3 engineers for 2 months.
        \end{itemize}
    \end{block}

    \begin{alertblock}{The "Bad Manager" Scenario}
        \textbf{Situation:} "10 man-months of work remain! But the deadline is in half a month (0.5 months)." \\
        \textbf{Bad Manager:} "Okay, let's add 20 people." (Since 10 / 0.5 = 20) \\
        \textbf{Result:} The project descends into chaos!
    \end{alertblock}

    \begin{itemize}
        \item An organized development process is crucial.
        \item (By the way, effort estimation is a subfield of software engineering.)
    \end{itemize}
\end{frame}

% Page 6
\begin{frame}{The Great Responsibility of Software}
    \begin{block}{The Key to Safety and Security in Daily Life}
        \begin{center}
            \textbf{Software Reliability}
        \end{center}
    \end{block}
    \begin{itemize}
        \item Software defects can cause serious failures.
        \begin{itemize}
            \item Potential to affect electricity, gas, water, transportation, finance.
            \item (e.g.) Stock market halt \rightarrow Heavy financial losses.
        \end{itemize}
        \item Potential to cause accidents.
        \begin{itemize}
            \item (e.g.) Elevator malfunction \rightarrow Fatal accident.
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{Testing under various situations is necessary.}
    \end{alertblock}
\end{frame}

% Page 7
\begin{frame}{Characteristics of Software (1/4)}
    \begin{block}{Difficult to Grasp the Actual State}
        \begin{itemize}
            \item Software is a logical, not physical, entity.
            \item In other words, it does not exist as a physical "thing".
        \end{itemize}
        \vfill
        It is difficult to grasp things like:
        \begin{itemize}
            \item How far has development progressed?
            \item Is it being built according to the specifications (intended standards)?
        \end{itemize}
        \begin{flushright}
            \tiny In contrast, in construction, the progress of work can be roughly grasped from the outside.
        \end{flushright}
    \end{block}
\end{frame}

% Page 8
\begin{frame}{Characteristics of Software (2/4)}
    \begin{block}{Work is Concentrated in the Development Process}
        \begin{itemize}
            \item For hardware, the flow is to develop a product and then manufacture it in a factory.
            \item For software, manufacturing is simply a copy operation.
        \end{itemize}
        \vfill
        \begin{itemize}
            \item Both quality and cost are centered on the development process.
            \item \textbf{Hardware}: Concern about producing defects during the manufacturing process.
            \item \textbf{Software}: A defective product means "the whole thing was flawed from the start".
            \item[$\rightarrow$] \alert{Everything depends on not failing in development.}
        \end{itemize}
        \begin{flushright}
            \tiny The games mentioned at the beginning are a typical example.
        \end{flushright}
    \end{block}
\end{frame}

% Page 9
\begin{frame}{Characteristics of Software (3/4)}
    \begin{block}{Long Operation and Maintenance Period}
        The period for software's:
        \begin{itemize}
            \item \textbf{Operation} (when the user actually uses it)
            \item \textbf{Maintenance} (performing modifications, improvements, extensions)
        \end{itemize}
        is much longer than the time it took to create it.
        \vfill
        \begin{itemize}
            \item There is no physical wear and tear, and once it's in use, it often becomes a "long-term relationship".
        \end{itemize}
        \begin{alertblock}{
            The ease of maintenance becomes a key point.
        }
        \end{alertblock}
    \end{block}
\end{frame}

% Page 10
\begin{frame}{Importance of Maintainability}
    \begin{block}{}
        For example, assume operation and maintenance spans 20 years.
        \begin{itemize}
            \item The people who created it will not be looking after it forever.
            \item[$\rightarrow$] Eventually, different people will take over maintenance.
            \item Therefore, it should be made into highly maintainable software.
        \end{itemize}
    \end{block}
    \begin{block}{"Legacy Systems"}
        There are many systems in the world, called "legacy systems," that have been in use for 20 or 30 years.
        \begin{itemize}
            \item The idea of "don't carelessly touch what is currently working" is deeply rooted in the field.
        \end{itemize}
        \begin{flushright}
            \tiny (e.g.) Financial systems written in COBOL.
        \end{flushright}
    \end{block}
\end{frame}

% Page 11
\begin{frame}{Characteristics of Software (4/4)}
    \begin{block}{Little Reuse}
        \begin{itemize}
            \item \textbf{Hardware}: It is common to reuse (divert) existing parts.
            \item \textbf{Software}: It is difficult to complete by just combining parts (various customizations are necessary).
        \end{itemize}
        \vfill
        \begin{itemize}
            \item Although reusable software exists in the form of libraries, it cannot be used without programming.
        \end{itemize}
    \end{block}
\end{frame}

% Page 12
\begin{frame}{Reuse}
    \begin{block}{Software reuse is broadly divided into two types}
        \begin{itemize}
            \item \textbf{Black-box Reuse}
            \begin{itemize}
                \item Use it like a component, without needing to care about the internals.
            \end{itemize}
            \vfill
            \item \textbf{White-box Reuse}
            \begin{itemize}
                \item "Copy and paste" the program, and rewrite it as necessary.
            \end{itemize}
        \end{itemize}
    \end{block}
\end{frame}

% Page 13
\begin{frame}{Black-Box Reuse}
    \begin{block}{In the form of a library}
        For example, use existing functions as they are, without concern for the source code's content. It can only be used in a predetermined way (according to the manual), but it is convenient.
        \begin{block}{Description of the diagram}
            The diagram illustrates the concept of black-box reuse. It shows a box labeled "Existing Code" containing examples like `scanf` and `printf` functions. An arrow points from this box to a new box labeled "New Code", with the label `#include <xxxx.h>` on the arrow. This signifies that the new code is incorporating the existing code as a library or module without needing to know its internal details, just how to use it through its public interface.
        \end{block}
        (e.g.) `scanf` and `printf` functions in C language.
    \end{block}
\end{frame}

% Page 14
\begin{frame}{White-Box Reuse}
    \begin{block}{In the form of "copying and pasting" source code}
        For example, reusing a part of existing source code. Transcribing code published in books or on web pages.
        \begin{itemize}
            \item When creating code using "good code" as a model, it is appropriate.
            \item However, there is also a risk of copying mistakes (bugs) or misuse.
            \begin{flushright}
                \tiny (e.g.) Copy-pasting a program you don't understand only leads to confusion.
            \end{flushright}
        \end{itemize}
        \begin{block}{Description of the diagram}
            The diagram explains white-box reuse. It depicts two code blocks, "Existing Code" and "New Code". A segment of code from the "Existing Code" block is highlighted and an arrow labeled "Copy" points from it to the "New Code" block, where the copied segment is integrated. This visualizes the process of copying and pasting a piece of code from one source to another for reuse, with the implication that the internal workings of the code are visible and can be modified.
        \end{block}
    \end{block}
\end{frame}

% Page 15
\begin{frame}{Exercise 1: Consider the problems of code clones}
    \begin{block}{}
        Something that is a direct copy-and-paste of source code, or a partially modified reuse of it, is called a "Code Clone".
    \end{block}
    \begin{block}{In the software industry, software with many code clones is considered problematic.}
        Why would that be?
    \end{block}
\end{frame}

% Page 16
\begin{frame}{Exercise 1 (Explanation: From the perspective of modification cost and risk)}
    \begin{block}{}
        Suppose a part of the code (a few lines) has been copied and pasted to 50 locations.
        \begin{itemize}
            \item It is common for problems to occur during operation or for requests for functional improvements to arise, making code modification necessary.
            \item[$\checkmark$] If the modification point is a code clone, all other 50 locations must also be modified (oversights are not permissible).
        \end{itemize}
        \begin{flushright}
            \tiny If it had been made into a "function" from the beginning, the modification would only be in one place...
        \end{flushright}
    \end{block}
\end{frame}

% Page 17
\begin{frame}{What is Good Software?}
    \begin{block}{Be aware that the concept differs depending on the viewpoint}
        \begin{columns}
            \begin{column}{.4\textwidth}
                \begin{center}
                    (Icon of a person representing a user) \\
                    \textbf{User or Customer}
                \end{center}
                \begin{itemize}
                    \item Easy to use
                    \item Meets requirements
                \end{itemize}
            \end{column}
            \begin{column}{.2\textwidth}
                \begin{center}
                    \large
                    \textbf{Good Software}
                \end{center}
            \end{column}
            \begin{column}{.4\textwidth}
                \begin{center}
                    (Icon of a person representing a developer) \\
                    \textbf{Developer}
                \end{center}
                \begin{itemize}
                    \item Easy to understand/maintain
                    \item Appropriate cost/period
                \end{itemize}
            \end{column}
        \end{columns}
        \begin{center}
            Software engineering mainly focuses on the developer's perspective.
        \end{center}
    \end{block}
\end{frame}

% Page 18
\begin{frame}{User's Perspective (1): Satisfaction of Requirements}
    \begin{block}{Satisfying the requirements from the customer (client)}
        Requirements are broadly classified into two types:
        \begin{itemize}
            \item \textbf{Functional Requirements}: Functions that should be implemented.
            \begin{itemize}
                \item (e.g.) The system must be able to authenticate users. (*"How" is a separate issue*)
            \end{itemize}
            \item \textbf{Non-functional Requirements}: "How" the requirements should be implemented.
            \begin{itemize}
                \item (e.g.) Authentication for 500 users must complete within 3 seconds.
            \end{itemize}
            \begin{flushright}
                \tiny Not about the function itself, but the way it's implemented, such as performance and security.
            \end{flushright}
        \end{itemize}
    \end{block}
\end{frame}

% Page 19
\begin{frame}{User's Perspective (2): Usability}
    \begin{block}{In a word, "ease of use"}
        \begin{itemize}
            \item The operation is easy to perform.
            \item The operation is easy to understand.
        \end{itemize}
    \end{block}
    \begin{block}{}
        Indirectly, operating speed and memory usage are also related (users don't want to use a so-called "heavy" system).
    \end{block}
\end{frame}

% Page 20
\begin{frame}{Developer's Perspective (1): Cost and Period}
    \begin{block}{Development cost should be low}
    \end{block}
    \begin{block}{Development period must be within the deadline}
        \begin{itemize}
            \item It's rare to be able to develop slowly over time.
            \item It's necessary to finish within the deadline with limited staff and time.
        \end{itemize}
    \end{block}
    \begin{alertblock}{
        The goal is to develop software that can be delivered at a low cost, within the deadline, and still meets the required specifications.
    }
    \end{alertblock}
\end{frame}

% Page 21
\begin{frame}{Developer's Perspective (2): Maintainability}
    \begin{block}{Once completed and in operation, requirements change almost always}
        \begin{itemize}
            \item Requests for minor modifications: "I want this part changed like this."
            \item Requests for adding/expanding functions: "I want more functions like this."
        \end{itemize}
    \end{block}
    \begin{block}{Defects (bugs) may also be found}
    \end{block}
    \begin{alertblock}{
        Easy-to-maintain software is "good" software. Since other people may maintain it, "understandability" is also important.
    }
    \end{alertblock}
\end{frame}

% Page 22
\begin{frame}{(Reference) 3-Letter Acronyms in Software Development}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{QCD}
                \begin{itemize}
                    \item Quality
                    \item Cost
                    \item Delivery
                \end{itemize}
                \begin{center}
                    Represents the three pillars of importance in the manufacturing industry.
                \end{center}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{KKD}
                \begin{itemize}
                    \item Kan (勘): Intuition
                    \item Keiken (経験): Experience
                    \item Dokyo (度胸): Guts
                \end{itemize}
                \begin{center}
                    Represents a site that is not well-managed.
                \end{center}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Page 23
\begin{frame}{10-minutes rest break}
    \begin{center}
        \Huge 10-Minute Break
    \end{center}
\end{frame}

% Page 24
\begin{frame}{Software Classification}
    \begin{center}
        \begin{tikzpicture}[node distance=1.5cm, auto]
            \node (user) {What the user wants to do};
            \node (app) [below of=user, draw, rectangle, minimum height=1cm, minimum width=3cm] {Application Software};
            \node (middle) [below of=app, draw, rectangle, minimum height=1cm, minimum width=3cm] {Middleware};
            \node (os) [below of=middle, draw, rectangle, minimum height=1cm, minimum width=3cm] {Basic Software};
            \node (computer) [below of=os] {What the computer can do};
            \draw[<->] (user) -- (app);
            \draw[<->] (os) -- (computer);
        \end{tikzpicture}
    \end{center}
    \begin{block}{Embedded Software}
        The image of these 3 layers being built into hardware.
    \end{block}
\end{frame}

% Page 25
\begin{frame}{Basic Software}
    \begin{block}{The foundational software for using a computer or running software}
        \begin{itemize}
            \item \textbf{OS (Operating System)} $\leftarrow$ This is often what "Basic Software" refers to.
            \item Compiler / Interpreter
            \item Service Programs (Utilities)
            \begin{itemize}
                \item (e.g.) File editing/searching
            \end{itemize}
        \end{itemize}
    \end{block}
    \begin{alertblock}{
        Basic software doesn't perform any specific business tasks, but without it, you can't get started.
    }
    \end{alertblock}
    \tiny (e.g.) Just because an OS (like Windows) is installed doesn't mean you can do anything. But if it's not installed, nothing works.
\end{frame}

% Page 26
\begin{frame}{Application Software (i.e., "Apps")}
    \begin{block}{Software with a specific purpose}
        For example:
        \begin{itemize}
            \item Securities Trading Management (Industry-specific software)
            \item Budget Management (Business-specific software)
            \item Spreadsheets, Presentations (Common application software)
        \end{itemize}
    \end{block}
    \begin{block}{Software becomes a tool to accomplish some kind of work or purpose}
    \end{block}
\end{frame}

% Page 27
\begin{frame}{Middleware}
    \begin{block}{Intermediate between basic software and application software}
        \begin{itemize}
            \item A relatively new concept, it's software that forms the foundation for application software.
            \item For example:
            \begin{itemize}
                \item Database Management System
                \item Web Server
                \item Application Server
            \end{itemize}
        \end{itemize}
        \begin{flushright}
            Software "for the app" rather than software the user uses directly.
        \end{flushright}
    \end{block}
    \begin{block}{
        Instead of using OS functions directly (via system calls, etc.), it's used at a higher-level concept: e.g., with SQL.
    }
    \end{block}
\end{frame}

% Page 28
\begin{frame}{Positioning of Middleware}
    \begin{block}{For example, suppose there is an application on Windows that processes a large amount of data}
        \begin{center}
            \begin{tikzpicture}[node distance=1.5cm, auto, >=stealth]
                \node (app) [draw, rectangle, minimum height=1.5cm, minimum width=4cm] {Application Software};
                \node (middle) [below of=app, yshift=-0.5cm, draw, rectangle, minimum height=1cm, minimum width=3cm] {Middleware};
                \node (db) [right of=middle, xshift=2cm, draw, circle, minimum size=1.5cm] {Database};
                \node (os) [below of=middle, yshift=-0.5cm, draw, rectangle, minimum height=1cm, minimum width=4cm] {Windows (OS)};

                \draw[->] (app) -- node[right, text width=4cm, midway] {Data search and management are not done in the application, but requested from the database (using SQL).} (middle);
                \draw[->] (middle) -- (db);
                \draw[->] (db) -- node[left, midway] {Return result} (app);
                \draw (app) -- (os);
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Page 29
\begin{frame}{Embedded Software}
    \begin{block}{Software mainly for machine control in electronic devices that realize specific functions}
        (*Not running on a general-purpose computer, but built into specific devices*)
        \begin{itemize}
            \item For example: Home appliances, car navigation systems, elevators, etc.
        \end{itemize}
    \end{block}
    \begin{block}{Supplied integrated with hardware}
        \begin{itemize}
            \item It's not easy to install a modified version.
            \item The operating environments are diverse, and high reliability is required.
        \end{itemize}
    \end{block}
\end{frame}

% Page 30
\begin{frame}{Exercise 2: Identify challenges in the development and maintenance of embedded software}
    \begin{block}{Consider the embedded software for elevator control}
        \begin{itemize}
            \item When developing this software, what is more difficult compared to general software?
            \item Also, what about when maintaining it?
        \end{itemize}
    \end{block}
\end{frame}

% Page 31
\begin{frame}{Exercise 2 (Example Answer)}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{Development Challenges}
                \begin{itemize}
                    \item \textbf{Need to maximize reliability}
                    \begin{itemize}
                        \item Runaway behavior is unacceptable.
                        \item Safe and stable operation is always required.
                    \end{itemize}
                    \item \textbf{Significant influence of hardware and environment}
                    \begin{itemize}
                        \item High temperature and humidity in summer.
                        \item Wear and physical failures also occur.
                    \end{itemize}
                \end{itemize}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{Maintenance Challenges}
                \begin{itemize}
                    \item \textbf{Modification is not easy}
                    \begin{itemize}
                        \item The elevator must be stopped.
                        \item Replacing hardware is difficult (e.g., adding a CPU or memory is not simple).
                    \end{itemize}
                    \item \textbf{Long operational period}
                    \begin{itemize}
                        \item 10 to 20 years is common.
                    \end{itemize}
                \end{itemize}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Page 32
\begin{frame}{Lifecycle}
    \begin{block}{Lifecycle: The entire life of a piece of software}
        \begin{center}
            \begin{tikzpicture}[node distance=1cm, auto, >=stealth, every node/.style={draw, rectangle, align=center}]
                \node (plan) {Development Plan};
                \node (dev) [below of=plan, fill=yellow!20] {(Development)};
                \node (req) [below of=dev] {Requirements Analysis};
                \node (ext) [below of=req] {External Design};
                \node (int) [below of=ext] {Internal Design};
                \node (impl) [below of=int] {Implementation \\ (Programming)};
                \node (test) [below of=impl] {Test};
                \node (op) [right of=impl, xshift=3cm, fill=green!20] {Operation \& Maintenance};
                \node (disc) [right of=op, xshift=3cm] {Discard};

                \draw[->] (plan) -- (req);
                \draw[->] (req) -- (ext);
                \draw[->] (ext) -- (int);
                \draw[->] (int) -- (impl);
                \draw[->] (impl) -- (test);
                \draw[->] (test) -- (op);
                \draw[->] (op) -- (disc);
                \draw[<->,dashed] (op) to [out=120,in=60] node[above,draw=none] {Usually, this is the longest part} (test);
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Page 33
\begin{frame}{Software Crisis}
    \begin{block}{A sense of crisis advocated in 1968}
        \begin{alertblock}{}
            Software development cannot keep up with needs, hindering the evolution of computer systems.
        \end{alertblock}
        \begin{itemize}
            \item Software development can't keep pace, impeding computer progress.
            \item Growing scale $\rightarrow$ Proliferation of bugs $\rightarrow$ Develops into a social problem.
            \item Increase in development costs.
        \end{itemize}
        \begin{center}
            \textbf{In short, software becomes the bottleneck!}
        \end{center}
    \end{block}
\end{frame}

% Page 34
\begin{frame}{(1) Hindering the Progress of Computers}
    \begin{block}{The direction was to "make computers do various things" (at the time)}
        \begin{center}
            Let's mass-produce general-purpose hardware cheaply, and have software handle the detailed responses.
        \end{center}
        \begin{alertblock}{But since software is basically "handmade":}
            \begin{itemize}
                \item Development can't keep up!
                \item There aren't enough engineers!
            \end{itemize}
        \end{alertblock}
    \end{block}
\end{frame}

% Page 35
\begin{frame}{(2) The Fear of Developing into a Social Problem}
    \begin{block}{As the needs for systems increase, software becomes larger and more complex}
        \begin{itemize}
            \item Naturally, the risk of human errors (so-called bugs) also increases.
            \begin{alertblock}{}
                Depending on the system and the type of bug, it could cause serious damage to social life.
                \begin{center}
                Potential to affect electricity, gas, water, transportation, finance, etc.
                \end{center}
            \end{alertblock}
        \end{itemize}
    \end{block}
\end{frame}

% Page 36
\begin{frame}{(3) Increase in Cost}
    \begin{block}{The cost of software development and maintenance is on the rise}
        \begin{center}
            \begin{tikzpicture}
                \draw[->] (0,0) -- (6,0) node[right] {Time};
                \draw[->] (0,0) -- (0,4) node[above] {Cost};
                \draw[blue, thick, ->] (0.5,3) .. controls (2,1) and (4,0.5) .. (5.5,0.8) node[above, text=blue, midway, text width=2cm, align=center] {\textbf{Hardware} \\ Benefit of tech progress and mass production};
                \draw[red, thick, ->] (0.5,1) .. controls (2,1.5) and (4,3) .. (5.5,3.5) node[above, text=red, midway, text width=2cm, align=center] {\textbf{Software} \\ Manual response to growing requirements and scale increases};
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Page 37
\begin{frame}{The Birth of "Software Engineering"}
    \begin{block}{Countermeasures are needed to solve such problems}
        Researching theories and techniques to efficiently develop high-quality software (until then, it was closer to "craftsmanship").
    \end{block}
    \begin{block}{Establish and systematize theories and techniques for software development as an "Engineering" discipline (the study of making things)}
        \begin{center}
            \alert{High productivity and high quality!}
        \end{center}
    \end{block}
\end{frame}

% Page 38
\begin{frame}{Current and Future Challenges}
    \begin{block}{The software industry has developed through various research and technological innovations, but difficult challenges still remain}
        \begin{itemize}
            \item \alert{Difficulty of requirements analysis}
            \item \alert{Difficulty of reuse}
            \item \alert{Difficulty of project management}
            \item \alert{Difficulty of estimation}
        \end{itemize}
    \end{block}
\end{frame}

% Page 39
\begin{frame}{Difficulty of Requirements Analysis}
    \begin{block}{Analyze customer requirements and describe them as valid specifications without ambiguity for the development purpose.}
        \begin{center}
            \textit{The first step, but the most human-centric and therefore full of difficulties.}
        \end{center}
        \begin{columns}
            \begin{column}{.5\textwidth}
                If the customer is an amateur in software, they might make surprisingly unreasonable requests.
                \begin{center}
                "You can't do that" type of conversations.
                \end{center}
            \end{column}
            \begin{column}{.5\textwidth}
                If the field (domain) is different, there's confusion due to different cultures and terminology.
                \begin{center}
                "We don't know what's common knowledge to them" type of conversations.
                \end{center}
            \end{column}
        \end{columns}
    \end{block}
\end{frame}

% Page 40
\begin{frame}{Difficulty of Reuse}
    \begin{block}{Source Code Reuse}
        \begin{itemize}
            \item Relatively easy to practice, but careless copy-and-paste can also cause bugs $\rightarrow$ Code Clones.
            \item Which code should I use?
        \end{itemize}
    \end{block}
    \begin{block}{Design and Architecture Reuse}
        \begin{itemize}
            \item Reuse of "knowledge and know-how" at a more abstract level than code, so the hurdle is quite high.
            \item Knowledge of design patterns, frameworks is necessary.
        \end{itemize}
    \end{block}
\end{frame}

% Page 41
\begin{frame}{Difficulty of Project Management}
    \begin{block}{In managing a development project, items such as:}
        \begin{itemize}
            \item Progress management
            \item Quality management
            \item Staff allocation, communication
        \end{itemize}
        are the challenges for the project manager.
    \end{block}
    \begin{block}{}
        For example, it might end up with only a few people running around in confusion, and efficient development for the whole team could not be achieved.
    \end{block}
\end{frame}

% Page 42
\begin{frame}{Difficulty of Estimation}
    \begin{block}{In reality, there is still a large element of personality}
        \begin{itemize}
            \item In other words, it largely depends on the experience and ability of individual engineers.
        \end{itemize}
    \end{block}
    \begin{block}{Estimating effort, period, and cost from there is, needless to say, difficult $\rightarrow$ Delays and cost overruns}
        \begin{itemize}
            \item Effort estimation models (COCOMO)
            \item Development organization maturity models (CMM, CMMI)
            \item Prediction by statistics, neural networks, etc.
        \end{itemize}
    \end{block}
\end{frame}

% Page 43
\begin{frame}{Summary}
    \begin{itemize}
        \item Software engineering is not just about how to make software, but also the discipline of management to lead a development project to success.
        \vfill
        \item It was born out of the software crisis (in that sense, its connection with the industry is strong).
        \vfill
        \item The software industry is developing, but there are still many challenges to be solved: requirements analysis, reuse, project management, estimation, etc.
    \end{itemize}
\end{frame}

% Page 44
\begin{frame}{Homework}
    \begin{center}
        \Huge Answer "[02] quiz"
        \Large (by this Friday 23:59)
    \end{center}
    \vfill
    \begin{block}{Note}
        Your quiz score will be a part of your final evaluation.
    \end{block}
\end{frame}

\end{document}