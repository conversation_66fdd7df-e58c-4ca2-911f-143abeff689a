\documentclass{beamer}
\usepackage[utf8]{inputenc}
\usepackage[vietnamese]{babel}
\usepackage{amsmath}
\usepackage{graphicx}
\usepackage{hyperref}

% Theme and color settings
\usetheme{default}
\usecolortheme{default}

% Title page information
\title{Kiểm Thử <PERSON>ầ<PERSON> \\ {[4]} Kỹ Thuật <PERSON>}
\author{Hirohisa <PERSON>an}
\institute{<EMAIL>}
\date{}

\begin{document}

% Frame 1: Title Page
\begin{frame}
  \titlepage
\end{frame}

% Frame 2: Test Process
\begin{frame}{Quy Trình <PERSON> (trong trường hợp <PERSON>nh T<PERSON>ác <PERSON>ước)}
    \begin{itemize}
        \item[\textrightarrow] \textbf{Quy Trình Phát Triển}
            \begin{itemize}
                \item[\textbullet] Phân Tích Yêu Cầu
                \item[\textbullet] Thiết <PERSON>
                \begin{itemize}
                    \item Thiết <PERSON>ê<PERSON>
                    \item Thiết <PERSON> Bên Trong
                \end{itemize}
                \item[\textbullet] <PERSON><PERSON> (<PERSON><PERSON><PERSON>)
                \item[\textbullet] \alert{Kiểm Thử}
                \item[\textbullet] Vận Hành \& Bảo Trì
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 3: Purpose and Content of Testing
\begin{frame}{Mục Đích và Nội Dung của Kiểm Thử}
    \textbf{Mục Đích}
    \begin{itemize}
        \item Tìm ra các lỗi tiềm ẩn (sai lầm, bug) có thể tồn tại trong phần mềm.
    \end{itemize}
    \vspace{1cm}
    \textbf{Nội Dung}
    \begin{itemize}
        \item Thực thi phần mềm và xác nhận xem nó có hoạt động đúng theo đặc tả hay không.
        \item Đồng thời, xác nhận càng nhiều càng tốt rằng không có lỗi xảy ra ngay cả trong các tình huống ngoại lệ không được chỉ định trong đặc tả.
    \end{itemize}
\end{frame}

% Frame 4: Difficulty of Testing
\begin{frame}{Khó Khăn của Kiểm Thử}
    \begin{itemize}
        \item Các nhà thiết kế và phát triển ban đầu có ý định tạo ra thứ gì đó hoạt động theo đặc tả.
        \vspace{1cm}
        \item Việc có tuân thủ đặc tả hay không có thể được kiểm tra cùng với tài liệu đặc tả (tuy nhiên, vẫn khó bao quát tất cả các trường hợp).
        \vspace{1cm}
        \item Đối với các tình huống không được chỉ định, rất khó kiểm tra vì chúng không được mong đợi hoặc không được chú ý ngay từ đầu.
    \end{itemize}
\end{frame}

% Frame 5: Importance of Testing
\begin{frame}{Tầm Quan Trọng của Kiểm Thử: Các trường hợp có thể đã được ngăn chặn}
    \begin{itemize}
        \item Mạng của AT\&T hoàn toàn ngừng hoạt động, 7,5 triệu cuộc gọi bị ngắt (1990)
            \begin{itemize}
                \item \textbf{Nguyên nhân:} Chỉ một dòng mã được thêm vào trong quá trình cập nhật.
            \end{itemize}
        \item Tên lửa Ariane 5 phát nổ giữa không trung (1996)
            \begin{itemize}
                \item \textbf{Nguyên nhân:} Một số 64-bit được xử lý như số 16-bit.
                \item ※ Chi phí phát triển hơn 8 tỷ đô la (gần 1 nghìn tỷ yên).
            \end{itemize}
        \item Tàu thám hiểm sao Hỏa rơi xuống bề mặt sao Hỏa (1999)
            \begin{itemize}
                \item \textbf{Nguyên nhân:} Đối với lực đẩy của động cơ hạ cánh, nhóm phân tích và nhóm vận hành sử dụng các đơn vị khác nhau: pound và newton.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 6: Terminology
\begin{frame}{Thuật Ngữ: Test Case, Test Suite, Testing Domain}
    \begin{itemize}
        \item \textbf{Test Case (Trường Hợp Kiểm Thử)}
            \begin{itemize}
                \item Một tập hợp cụ thể các dữ liệu/điều kiện đầu vào (và đầu ra mong đợi).
            \end{itemize}
        \item \textbf{Test Suite (Bộ Kiểm Thử)}
            \begin{itemize}
                \item Một tập hợp các trường hợp kiểm thử để thực hiện một loạt các kiểm thử.
            \end{itemize}
        \item \textbf{Testing Domain (Miền Kiểm Thử)}
            \begin{itemize}
                \item Toàn bộ tập hợp các trường hợp kiểm thử có thể đảm bảo một chương trình không có lỗi.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 7: Test Example (1): Length Conversion
\begin{frame}{Ví Dụ Kiểm Thử (1): Chuyển Đổi Độ Dài}
    \textbf{Chương Trình Chuyển Đổi "Kilometer \textrightarrow Mile"} \hfill \footnotesize{※1 kilometer = 0.621371 miles}
    \vspace{0.5cm}
    \textbf{Đầu vào:} Một số thực từ 0 đến (nhưng không bao gồm) 10000. Hợp lệ đến 2 chữ số thập phân (các chữ số vượt quá sẽ bị bỏ qua).
    \vspace{0.5cm}
    \textbf{Đầu ra:} Giá trị được chuyển đổi sang mile, cắt bớt đến 3 chữ số thập phân.
    \vspace{0.5cm}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{tabular}{|l|l|}
                \hline
                \textbf{Đầu vào} & \textbf{Đầu ra Mong đợi} \\ \hline
                0 & 0.000 \\ \hline
                9999.99 & 6213.703 \\ \hline
                1 & 0.621 \\ \hline
                1.001 & 0.621 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{tabular}{|l|l|}
                \hline
                \textbf{Đầu vào} & \textbf{Đầu ra Mong đợi} \\ \hline
                9.99 & 6.207 \\ \hline
                10 & 6.213 \\ \hline
                -1 & Lỗi \\ \hline
                10000 & Lỗi \\ \hline
            \end{tabular}
        \end{column}
    \end{columns}
    \begin{flushright}
        \small Cũng nên xem xét các trường hợp ngoại lệ (ví dụ: đầu vào không phải số).
    \end{flushright}
\end{frame}

% Frame 8: Test Example (2): Triangle Problem
\begin{frame}{Ví Dụ Kiểm Thử (2): Bài Toán Tam Giác (\textit{※Một bài toán nổi tiếng})}
    Xem xét một tam giác được tạo thành bởi ba số nguyên làm đầu vào, đại diện cho độ dài các cạnh của nó. Hãy tạo các trường hợp kiểm thử cho một chương trình xác định xem nó có phải là:
    \begin{itemize}
        \item Tam giác cân
        \item Tam giác đều
        \item Tam giác thường
    \end{itemize}
\end{frame}

% Frame 9: Test Case Examples for the Triangle Problem
\begin{frame}{Ví Dụ Trường Hợp Kiểm Thử cho Bài Toán Tam Giác}
    \begin{tabular}{|l|l|}
        \hline
        \textbf{Đầu vào} & \textbf{Đầu ra Mong đợi} \\ \hline
        (2, 5, 5) & Tam giác cân \\ \hline
        (5, 5, 5) & Tam giác đều \\ \hline
        (3, 4, 5) & Tam giác thường \\ \hline
        (0, 0, 0) & Lỗi ※Trở thành một điểm \\ \hline
        (3, 4, 7) & Lỗi ※Trở thành một đoạn thẳng \\ \hline
        (2, 5, 8) & Lỗi ※Cạnh dài nhất > tổng các cạnh khác \\ \hline
        (1.3, 4, 5) & Lỗi ※Chứa số thực (bất đẳng thức thỏa mãn) \\ \hline
        (-2, 4, 5) & Lỗi ※Chứa số âm \\ \hline
    \end{tabular}
    \vspace{0.5cm}
    \small \textit{※Bất đẳng thức tam giác: Độ dài cạnh dài nhất phải nhỏ hơn tổng độ dài của hai cạnh còn lại.}
    \newline
    \small Cũng nên xem xét các trường hợp thay đổi thứ tự đầu vào của các số.
\end{frame}

% Frame 10: Exercise 1
\begin{frame}{Bài Tập 1}
    \textbf{Kiểm thử một chương trình sắp xếp số nguyên.}
    \vspace{0.5cm}
    Bạn được cho một chương trình sắp xếp N số nguyên.
    \begin{itemize}
        \item 0 ≤ N ≤ 10000
        \item Đầu vào từ một file (nơi lưu trữ một chuỗi số).
    \end{itemize}
    \vspace{0.5cm}
    Hãy nghĩ về các trường hợp kiểm thử cho chương trình này (ở đây, chỉ cần đầu vào là đủ).
\end{frame}

% Frame 11: What's Important in Testing
\begin{frame}{Điều Quan Trọng trong Kiểm Thử}
    \textbf{Ghi lại kết quả}
    \begin{itemize}
        \item Trường hợp kiểm thử nào tìm thấy loại lỗi gì, ai tìm thấy, và khi nào.
        \item Điều này trở thành thông tin quan trọng cho việc sửa chữa sau này.
    \end{itemize}
    \vspace{1cm}
    \textbf{Giao tiếp phù hợp}
    \begin{itemize}
        \item Thông tin mơ hồ không hữu ích. \hfill \textit{\small Báo cáo chỉ nói "nó không hoạt động" là vô nghĩa.}
        \item Báo cáo lỗi là quan trọng, nhưng cách bạn giao tiếp cũng quan trọng.
        \item Bạn không nên trình bày phát hiện lỗi cho các nhà phát triển với giọng điệu tự hào.
        \item Có những nỗ lực mà chỉ người tạo ra nó mới biết, vì vậy mối quan hệ con người cũng quan trọng.
    \end{itemize}
\end{frame}

% Frame 12: Testability
\begin{frame}{Khả Năng Kiểm Thử}
    Khả năng kiểm thử (dễ dàng kiểm thử) là
    \begin{itemize}
        \item Một đặc tính quan trọng liên kết trực tiếp với
        \begin{itemize}
            \item Giảm chi phí (giờ công) dành cho kiểm thử
            \item Rút ngắn thời gian kiểm thử
        \end{itemize}
        \item Tự nhiên dẫn đến cải thiện chất lượng.
    \end{itemize}
\end{frame}

% Frame 13: Improving Testability
\begin{frame}{Cải Thiện Khả Năng Kiểm Thử}
    \textbf{Cần xem xét trong thiết kế và triển khai}
    \begin{itemize}
        \item Quan trọng là làm rõ chức năng nào tương ứng với phần nào của chương trình.
        \item Thiết kế với việc kiểm thử trong tâm trí.
            \begin{itemize}
                \item Ví dụ: Bằng cách chia hệ thống thành các chức năng (chương trình con) tốt, chúng có thể được kiểm thử độc lập.
            \end{itemize}
    \end{itemize}
    \vspace{1cm}
    \textbf{Luôn nghĩ đến kiểm thử khi tạo}
    \begin{itemize}
        \item Làm cho dễ hiểu chương trình đang làm gì để nếu phát hiện lỗi trong quá trình kiểm thử, có thể xử lý ngay lập tức.
        \item Tạo ra mọi thứ một cách tùy tiện (với thái độ "chỉ cần làm cho nó hoạt động bây giờ") thẳng thắn là lãng phí thời gian và công sức.
    \end{itemize}
\end{frame}

% Frame 14: V-Model Correspondence
\begin{frame}{Tương Ứng Mô Hình V}
    \begin{itemize}
        \item \textbf{Kiểm Thử Đơn Vị/Tích Hợp:} Xác nhận hoạt động của module
        \item \textbf{Kiểm Thử Hệ Thống trở lên:} Xác nhận hoạt động của hệ thống
    \end{itemize}
    \begin{center}
        \includegraphics[width=0.8\textwidth]{v-model.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "v-model.png" hiển thị mô hình V của phát triển phần mềm. Ở phía bên trái, từ trên xuống dưới, là "Phân Tích Yêu Cầu", "Thiết Kế Bên Ngoài", "Thiết Kế Bên Trong", và "Triển Khai (Lập Trình)". Ở phía bên phải, từ dưới lên trên, là "Kiểm Thử Đơn Vị/Tích Hợp", "Kiểm Thử Hệ Thống", và "Kiểm Thử Chấp Nhận/Vận Hành". Các mũi tên hiển thị sự tương ứng giữa phía trái và phía phải.}
\end{frame}

% Frame 15: Module
\begin{frame}{Module}
    Module là một đơn vị có thể tách rời của chương trình.
    \begin{itemize}
        \item Nó đề cập đến một phần của chương trình (thành phần phần mềm) có thể được thay thế ở mức độ đó.
        \item Trong C, điều này tương ứng với một "hàm" hoặc "một tập hợp của nhiều hàm (thường là một chương trình)".
        \item Trong các ngôn ngữ hướng đối tượng như Java, một "lớp" tương ứng với điều này (※ Một "phương thức" tương đương với một "hàm", nhưng một phương thức không thể tồn tại độc lập).
    \end{itemize}
\end{frame}

% Frame 16: Unit Test, Integration Test
\begin{frame}{Kiểm Thử Đơn Vị, Kiểm Thử Tích Hợp}
    \textbf{Kiểm Thử Đơn Vị}
    \begin{itemize}
        \item Đối với một module đơn lẻ, các đầu vào khác nhau được đưa ra để kiểm tra xem có nhận được đầu ra phù hợp/đúng hay không.
    \end{itemize}
    \vspace{1cm}
    \textbf{Kiểm Thử Tích Hợp}
    \begin{itemize}
        \item Nhiều module (đã vượt qua kiểm thử đơn vị) được kết hợp để kiểm tra xem các module có thể thực hiện I/O phù hợp trong trạng thái có mối quan hệ kết nối hay không.
    \end{itemize}
\end{frame}

% Frame 17: System Test, Acceptance/Operational Test
\begin{frame}{Kiểm Thử Hệ Thống, Kiểm Thử Chấp Nhận/Vận Hành}
    \textbf{Kiểm Thử Hệ Thống}
    \begin{itemize}
        \item Để xác nhận xem hệ thống có hoạt động theo đặc tả hay không.
    \end{itemize}
    \vspace{1cm}
    \textbf{Kiểm Thử Chấp Nhận/Vận Hành}
    \begin{itemize}
        \item Tương tự như kiểm thử hệ thống, nhưng đây là kiểm thử dưới môi trường vận hành thực tế, có nghĩa là kiểm thử bởi khách hàng/người dùng.
        \item Thực sự để họ sử dụng nó (ví dụ: cho công việc).
    \end{itemize}
\end{frame}

% Frame 18: Classification of Tests
\begin{frame}{Phân Loại Kiểm Thử}
    \begin{itemize}
        \item \textbf{Kiểm Thử Hộp Đen}
            \begin{itemize}
                \item Bên trong chương trình không được xem xét (được coi như một hộp đen), và kiểm thử hoạt động được thực hiện dựa trên đặc tả.
            \end{itemize}
        \item \textbf{Kiểm Thử Hộp Trắng}
            \begin{itemize}
                \item Kiểm thử hoạt động được thực hiện dựa trên cấu trúc bên trong của chương trình (chủ yếu là sơ đồ luồng).
            \end{itemize}
        \item \textbf{Kiểm Thử Ngẫu Nhiên}
            \begin{itemize}
                \item Các trường hợp kiểm thử được tạo ngẫu nhiên để thực hiện kiểm thử hoạt động.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 19: Black Box Testing Technique (1)
\begin{frame}{Kỹ Thuật Kiểm Thử Hộp Đen (1): Phân Vùng Tương Đương}
    Để thiết kế các trường hợp kiểm thử một cách hiệu quả, không gian đầu vào được chia dựa trên một loại quan hệ tương đương.
    \begin{itemize}
        \item Một phân vùng được gọi là "lớp tương đương".
        \item Lớp tương đương là một tập hợp các đầu vào được mong đợi sẽ được chương trình xử lý giống nhau cho mỗi điều kiện đầu vào.
        \item (Có hai loại lớp tương đương)
            \begin{itemize}
                \item \textbf{Lớp Tương Đương Hợp Lệ:} Những thứ hợp lệ làm đầu vào.
                \item \textbf{Lớp Tương Đương Không Hợp Lệ:} Những thứ không hợp lệ làm đầu vào.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 20: Equivalence Relation
\begin{frame}{Quan Hệ Tương Đương}
    Một quan hệ (ký hiệu bằng R) giữa các phần tử của một tập hợp thỏa mãn tất cả các tính chất sau:
    \begin{itemize}
        \item \textbf{Phản xạ:} xRx
        \item \textbf{Đối xứng:} nếu xRy thì yRx
        \item \textbf{Bắc cầu:} nếu xRy và yRz thì xRz
    \end{itemize}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \textbf{Ví dụ về R là quan hệ tương đương:}
            \begin{itemize}
                \item = (bằng)
                \item || (các đường thẳng song song)
                \item "số dư khi chia cho 10 giống nhau"
            \end{itemize}
        \end{column}
        \begin{column}{0.5\textwidth}
            \textbf{Ví dụ về R KHÔNG phải quan hệ tương đương:}
            \begin{itemize}
                \item < (nhỏ hơn)
                \item "(hình dạng) chồng lấp"
                \item "(x) biết (y)"
            \end{itemize}
        \end{column}
    \end{columns}
    \vspace{0.5cm}
    \textit{Nếu bạn quyết định một ví dụ đại diện, bạn có thể tạo thành một nhóm (lớp tương đương) của những thứ có quan hệ tương đương với nó.}
\end{frame}

% Frame 21: Break
\begin{frame}
    \begin{center}
        \Huge Nghỉ 10 phút
    \end{center}
\end{frame}

% Frame 22: Equivalence Partitioning Example 1 (1/2)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 1 (1/2)}
    \textbf{Đầu vào:} Điểm môn học (giá trị số nguyên)
    \newline
    \textbf{Đầu ra:} Một trong "S, A, B, C, D, NA"
    \vspace{1cm}
    \textbf{Đặc tả:}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{itemize}
                \item 90\textasciitilde100 \textrightarrow S
                \item 70\textasciitilde79 \textrightarrow B
                \item 0\textasciitilde59 \textrightarrow D
            \end{itemize}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{itemize}
                \item 80\textasciitilde89 \textrightarrow A
                \item 60\textasciitilde69 \textrightarrow C
                \item -1 \textrightarrow NA
            \end{itemize}
        \end{column}
    \end{columns}
\end{frame}

% Frame 23: Equivalence Partitioning Example 1 (2/2)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 1 (2/2)}
    Các lớp tương đương hợp lệ và không hợp lệ như sau:
    \begin{center}
        \begin{tabular}{|c|c|}
            \hline
            \textbf{Lớp Tương Đương Hợp Lệ} & \textbf{Lớp Tương Đương Không Hợp Lệ} \\ \hline
            90 \textasciitilde 100 & 101 trở lên \\ \hline
            80 \textasciitilde 89 & -2 trở xuống \\ \hline
            70 \textasciitilde 79 & \\ \hline
            60 \textasciitilde 69 & \\ \hline
            0 \textasciitilde 59 & \\ \hline
            -1 & \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \textit{Chọn một giá trị từ mỗi lớp tương đương để thực hiện kiểm thử.}
\end{frame}

% Frame 24: Equivalence Partitioning Example 2 (1/4)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 2 (1/4)}
    \textbf{Chương Trình Tính Phí Đỗ Xe}
    \newline
    \textbf{Đầu vào (1): Thời gian đỗ xe (đơn vị: phút)}
    \begin{itemize}
        \item 1 phút \textasciitilde 30 phút = 100 yên
        \item 31 phút \textasciitilde 60 phút = 200 yên
        \item Sau đó = 100 yên tăng thêm mỗi giờ
    \end{itemize}
    \textbf{Đầu vào (2): Số tiền mua hàng tại cửa hàng đối tác}
    \begin{itemize}
        \item 2.000 yên trở lên được miễn phí 1 giờ
    \end{itemize}
    \small\textit{※Nếu thời gian đỗ xe vượt quá 1 giờ, chỉ 200 yên (cho giờ đầu tiên) được giảm giá.}
\end{frame}

% Frame 25: Equivalence Partitioning Example 2 (2/4)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 2 (2/4)}
    \begin{center}
        \begin{tabular}{|l|c|c|}
            \hline
            \textbf{Điều Kiện Đầu Vào} & \textbf{Lớp Tương Đương Hợp Lệ} & \textbf{Lớp Tương Đương Không Hợp Lệ} \\ \hline
            (1) Thời Gian Đỗ Xe & 1\textasciitilde30 & 0 trở xuống \\ \cline{2-2}
             & 31\textasciitilde60 & \\ \cline{2-2}
             & 61 trở lên & \\ \hline
            (2) Số Tiền Mua Hàng & 0\textasciitilde1999 & -1 trở xuống \\ \cline{2-2}
             & 2000 trở lên & \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \small\textit{※Giá trị âm cho thời gian hoặc số tiền là không thể, nhưng ở đây chúng ta giả sử chúng có thể được "nhập vào".}
    \newline
    \small\textit{Các lớp được nhóm lại với nhau vì phương pháp tính toán giống nhau.}
\end{frame}

% Frame 26: Equivalence Partitioning Example 2 (3/4)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 2 (3/4)}
    Đầu tiên, đối với hai điều kiện đầu vào, tạo các kết hợp của các lớp tương đương hợp lệ.
    \begin{center}
        \includegraphics[width=\textwidth]{table1.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "table1.png" hiển thị một bảng để tạo các trường hợp kiểm thử. Các hàng đại diện cho điều kiện "Thời Gian Đỗ Xe" (Hợp lệ: 1-30, 31-60, 61-; Không hợp lệ: ~0) và "Số Tiền Mua Hàng" (Hợp lệ: 0-1999, 2000-; Không hợp lệ: âm). Các cột là kết hợp của những điều này. Các vòng tròn chỉ ra kết hợp nào cần kiểm thử. Một cột dọc các vòng tròn đại diện cho một trường hợp kiểm thử. Một ví dụ được đưa ra: đỗ xe 70 phút, mua hàng 2100 yên -> Phí đỗ xe 100 yên (= 300 - 200).}
\end{frame}

% Frame 27: Equivalence Partitioning Example 2 (4/4)
\begin{frame}{Ví Dụ Phân Vùng Tương Đương 2 (4/4)}
    Tiếp theo, tạo các kết hợp chỉ bao gồm một lớp tương đương không hợp lệ.
    \begin{center}
        \includegraphics[width=\textwidth]{table2.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "table2.png" tương tự như hình trước, nhưng lần này nó hiển thị các kết hợp trong đó một trong các điều kiện không hợp lệ. Một ví dụ được đưa ra: đỗ xe 0 phút, mua hàng 2100 yên -> Hiển thị lỗi cho đầu vào không hợp lệ.}
\end{frame}

% Frame 28: Procedure for Equivalence Partitioning
\begin{frame}{Quy Trình Phân Vùng Tương Đương}
    \begin{enumerate}
        \item Đặt các lớp tương đương hợp lệ và không hợp lệ cho mỗi điều kiện đầu vào.
        \item Tạo tất cả các kết hợp của các lớp tương đương hợp lệ.
        \item Tạo các kết hợp chỉ bao gồm một lớp tương đương không hợp lệ.
    \end{enumerate}
    \vspace{0.5cm}
    \small\textit{※Tất nhiên, bạn có thể thêm các kiểm thử với hai hoặc nhiều lớp không hợp lệ, nhưng tốt hơn là trước tiên kiểm tra rằng không có vấn đề gì phát sinh khi chỉ trộn vào một lớp không hợp lệ.}
\end{frame}

% Frame 29: Black Box Testing Method (2)
\begin{frame}{Phương Pháp Kiểm Thử Hộp Đen (2): Phân Tích Giá Trị Biên}
    Tập trung vào "điểm thay đổi" (biên) của các điều kiện chương trình.
    \begin{itemize}
        \item Kiểm thử tại các biên của điều kiện đầu vào và đầu ra.
        \item Kiểm thử tại các cạnh hoặc biên của các lớp tương đương hợp lệ và không hợp lệ, hoặc ngay bên ngoài chúng.
    \end{itemize}
    \begin{center}
        \includegraphics[width=0.8\textwidth]{boundary.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "boundary.png" minh họa các điểm kiểm thử xung quanh một biên. Nó hiển thị một đường giá trị đầu vào với các phần cho "Không hợp lệ", "Hợp lệ", và "Không hợp lệ". Các mũi tên chỉ đến các biên chính xác và các điểm ngay bên trong và bên ngoài phạm vi hợp lệ, chỉ ra đây là các điểm cần kiểm thử.}
\end{frame}

% Frame 30: Why focus on condition boundaries?
\begin{frame}{Tại sao tập trung vào biên điều kiện?}
    Thật đáng ngạc nhiên là việc mắc lỗi trong các phần kiểm tra điều kiện của chương trình rất phổ biến.
    \vspace{0.5cm}
    (Ví dụ) Một lỗi trong biểu thức điều kiện của câu lệnh if trong chương trình đánh giá điểm:
    \begin{verbatim}
    if (score > 90) {
        printf("S\n");
    }
    \end{verbatim}
    \begin{itemize}
        \item \textit{Điểm 90 không dẫn đến "S".}
        \item \textit{Điểm 101 dẫn đến "S" thay vì "lỗi".}
    \end{itemize}
    \vspace{0.5cm}
    Đúng:
    \begin{verbatim}
    if (score >= 90 && score <= 100) {
        printf("S\n");
    }
    \end{verbatim}
\end{frame}

% Frame 31: Boundary-Value Analysis Example
\begin{frame}{Ví Dụ Phân Tích Giá Trị Biên}
    Xem xét một chương trình xuất ra một số thập phân (0 đến nhỏ hơn 16) dưới dạng nhị phân.
    \begin{center}
        \begin{tabular}{|c|c|c|l|}
            \hline
            \multicolumn{2}{|c|}{\textbf{Biên Điều Kiện}} & \multicolumn{2}{c|}{\textbf{Cạnh của Lớp Tương Đương}} \\
            \multicolumn{2}{|c|}{\textbf{(tập trung vào bit đầu ra)}} & \multicolumn{2}{c|}{\textbf{và bên ngoài của nó}} \\ \hline
            (Đầu vào) & & & \\ \hline
            0, 1 & 1-bit & 0 & Giá trị tối thiểu hợp lệ \\ \hline
            2, 3 & 2-bit & 15 & Giá trị tối đa hợp lệ \\ \hline
            4, 7 & 3-bit & -1 & Không hợp lệ \\ \hline
            8, 15 & 4-bit & 16 & Không hợp lệ \\ \hline
        \end{tabular}
    \end{center}
    \vspace{0.5cm}
    \textit{Cũng nên có các lớp tương đương không hợp lệ khác, như đầu vào không phải số nguyên.}
\end{frame}

% Frame 32: Exercise 2
\begin{frame}{Bài Tập 2}
    \textbf{Thiết kế các trường hợp kiểm thử sử dụng phân tích giá trị biên.}
    \vspace{0.5cm}
    \textbf{Mục Tiêu Kiểm Thử:} Một chương trình nhận hai số nguyên (0 đến nhỏ hơn 10) làm đầu vào và xuất tổng của chúng dưới dạng thập lục phân.
    \begin{itemize}
        \item Đầu ra là 1 chữ số nếu tổng là 0-15, 2 chữ số nếu 16-18.
        \item Đối với đầu vào không hợp lệ, hiển thị ERROR.
    \end{itemize}
\end{frame}

% Frame 33: (Reference: Other Methods) Using Orthogonal Arrays
\begin{frame}{(Tham Khảo: Các Phương Pháp Khác) Sử Dụng Mảng Trực Giao}
    Khi có nhiều chức năng cần được kiểm thử, việc bao phủ các kết hợp của chúng cũng quan trọng.
    \begin{itemize}
        \item Đơn giản chọn 2 trong n chức năng dẫn đến n(n-1)/2 kết hợp, và số lượng kết hợp có thể trở nên rất lớn.
        \item Ví dụ, với n=10 là 45 kết hợp, với n=20 là 190, với n=30 là 435.
    \end{itemize}
    \vspace{0.5cm}
    \textbf{Mảng trực giao có thể được sử dụng để tạo kết hợp một cách hiệu quả.}
\end{frame}

% Frame 34: Orthogonal Array
\begin{frame}{Mảng Trực Giao}
    Ví dụ, trong trang trí ký tự của trình xử lý văn bản:
    \newline
    A = Đậm, B = Nghiêng, C = Gạch chân, D = Đổ bóng
    \begin{columns}
        \begin{column}{0.4\textwidth}
            \textbf{Bao phủ 2 chức năng (A,B)}
            \begin{tabular}{|c|c|}
                \hline
                A & B \\ \hline
                0 & 0 \\ \hline
                0 & 1 \\ \hline
                1 & 0 \\ \hline
                1 & 1 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{0.6\textwidth}
            \textbf{Đối với 4 chức năng (A,B,C,D), 8 kết hợp bên phải là OK.}
            \begin{tabular}{|c|c|c|c|}
                \hline
                A & B & C & D \\ \hline
                0 & 0 & 0 & 0 \\ \hline
                0 & 0 & 0 & 1 \\ \hline
                0 & 1 & 1 & 0 \\ \hline
                0 & 1 & 1 & 1 \\ \hline
                1 & 0 & 1 & 0 \\ \hline
                1 & 0 & 1 & 1 \\ \hline
                1 & 1 & 0 & 0 \\ \hline
                1 & 1 & 0 & 1 \\ \hline
            \end{tabular}
        \end{column}
    \end{columns}
    \textit{Nếu bạn lấy bất kỳ hai cột nào, các cặp (0,0), (0,1), (1,0), (1,1) xuất hiện cùng số lần. Loại bảng này được gọi là mảng trực giao và hiệu quả trong kiểm thử kết hợp.}
\end{frame}

% Frame 35: How to make an orthogonal array
\begin{frame}{Cách tạo mảng trực giao}
    Cơ bản là 3 bước:
    \begin{center}
        \includegraphics[width=0.9\textwidth]{ortho1.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "ortho1.png" hiển thị quy trình 3 bước. 1. Bắt đầu với một cột 0 và 1. Nhân đôi mỗi hàng. 2. Tạo một cột mới, xen kẽ 0 và 1. 3. Tạo một cột mới bằng cách lấy XOR của các cột hiện có.}
\end{frame}

% Frame 36: How to make an orthogonal array (continued)
\begin{frame}{Cách tạo mảng trực giao (tiếp tục)}
    (Vòng thứ 2)
    \begin{center}
        \includegraphics[width=\textwidth]{ortho2.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "ortho2.png" hiển thị sự tiếp tục của quy trình để thêm nhiều cột hơn. Quy trình từ slide trước được lặp lại để mở rộng mảng. Được đề cập rằng có thể thêm 2 cột nữa.}
\end{frame}

% Frame 37: Exercise 3
\begin{frame}{Bài Tập 3}
    \textbf{Thêm hai cột nữa vào mảng trực giao.}
    \vspace{0.5cm}
    Trong mảng trực giao trước, có thể thêm hai cột nữa. Hãy tìm chúng.
    \begin{center}
        \includegraphics[width=0.8\textwidth]{ortho3.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "ortho3.png" hiển thị mảng trực giao 8x5 từ slide trước và yêu cầu tìm hai cột còn lại.}
\end{frame}

% Frame 38: Effectiveness of 2-function combinations
\begin{frame}{Hiệu Quả của Kết Hợp 2 Chức Năng}
    \textbf{Mối quan hệ giữa số lượng chức năng kết hợp và lỗi: Tỷ lệ phát hiện lỗi (\%)}
    \begin{center}
        \includegraphics[width=\textwidth]{fault_table.png}
    \end{center}
    \textit{Lưu ý: Hình ảnh "fault_table.png" là một bảng hiển thị tỷ lệ phát hiện lỗi cho các hệ thống khác nhau (Hệ thống chuyên gia, OS, Thiết bị y tế nhúng, Mozilla, Apache, DB) dựa trên số lượng chức năng tương tác (1 đến 6). Dữ liệu cho thấy rằng kiểm thử kết hợp 2 chiều tìm thấy một tỷ lệ đáng kể các lỗi.}
    \newline
    \tiny{D.R. Kuhn, et al., "Software fault interactions and implications for software testing," IEEE Trans. Softw. Eng., vol.30, no.6, pp.418-421, June 2004.}
\end{frame}

% Frame 39: Summary
\begin{frame}{Tóm Tắt}
    \begin{itemize}
        \item Kiểm thử: Nhiệm vụ tìm ra "lỗi".
            \begin{itemize}
                \item Đây là chìa khóa để đảm bảo chất lượng.
                \item Cần có tư duy linh hoạt.
                \item Ví dụ: Bài toán tam giác.
            \end{itemize}
        \item Kiểm thử hộp đen: Kiểm tra hành vi từ bên ngoài.
            \begin{itemize}
                \item \textbf{Phân vùng tương đương:} Kết hợp các lớp tương đương hợp lệ và không hợp lệ.
                \item \textbf{Phân tích giá trị biên:} Tập trung vào biên của điều kiện I/O và lớp tương đương.
            \end{itemize}
    \end{itemize}
\end{frame}

% Frame 40: Notice
\begin{frame}{Thông Báo}
    Lớp học tiếp theo là lớp thực hành nơi bạn sẽ tạo và kiểm thử các chương trình C trên PC của riêng mình.
    \begin{itemize}
        \item Tất cả sinh viên phải phát triển và kiểm thử các chương trình C trên PC của bạn.
    \end{itemize}
    \vspace{0.5cm}
    Bạn phải hoàn thành việc cài đặt trình biên dịch C trên PC của mình (khuyến nghị: gcc).
\end{frame}

% Frame 41: Homework
\begin{frame}{Bài Tập Về Nhà}
    \begin{center}
        \Huge Trả lời "[04] quiz"
        \Large (trước thứ Sáu 23:59)
    \end{center}
    \vspace{1cm}
    \textit{Lưu ý: Điểm quiz của bạn sẽ là một phần của đánh giá cuối cùng.}
\end{frame}

% Frame 42: Exercise 1 Answer Example
\begin{frame}{【Bài Tập 1】Ví Dụ Trả Lời}
    \begin{itemize}
        \item Chuỗi số ngẫu nhiên
            \begin{itemize}
                \item \{5, 1, 9, 2, 3, 7, ...\}
            \end{itemize}
        \item Chuỗi số đã sắp xếp
            \begin{itemize}
                \item \{1, 2, 3, 4, ...\}
                \item \{100, 99, 98, 97, ...\}
            \end{itemize}
        \item Trường hợp chỉ có 1 mục, trường hợp có nhiều hơn N mục
            \begin{itemize}
                \item \{2\}
                \item \{1, 2, 3, ..., N, N+1\}
            \end{itemize}
        \item Trường hợp có 0 mục
            \begin{itemize}
                \item \{\}
            \end{itemize}
    \end{itemize}
    \textit{Cũng xem xét các ngoại lệ như không tìm thấy file, không mở được, v.v.}
\end{frame}

% Frame 43: Exercise 2 Answer Example
\begin{frame}{【Bài Tập 2】Ví Dụ Trả Lời}
    \begin{center}
        \begin{tabular}{|c|c|c|c|}
            \hline
            \multicolumn{2}{|c|}{\textbf{Biên Điều Kiện}} & \multicolumn{2}{c|}{\textbf{Cạnh của Lớp Tương Đương và Bên Ngoài}} \\ \hline
            6+9 & 1 chữ số & 0+0 & Giá trị tối thiểu hợp lệ \\ \hline
            8+7 & & 9+9 & Giá trị tối đa hợp lệ \\ \hline
            7+9 & & -1+0 & Không hợp lệ \\ \hline
            8+8 & 2 chữ số & 10+0 & Không hợp lệ \\ \hline
            9+7 & & 10+10 & Không hợp lệ \\ \hline
            5+5 & Ký hiệu & -1+10 & Không hợp lệ \\ \hline
        \end{tabular}
    \end{center}
    \textit{Cũng nên có các lớp tương đương không hợp lệ khác, như đầu vào không phải số nguyên.}
    \newline
    \tiny{※Đối với trường hợp 1 chữ số, cả 6+9 và 8+7 đều được xem xét để tính đến cả hai mẫu độ lớn số (không bắt buộc). Tương tự cho trường hợp 2 chữ số.}
\end{frame}

% Frame 44: Exercise 3 Answer
\begin{frame}{【Bài Tập 3】Đáp Án}
    \textbf{Thêm hai cột nữa vào mảng trực giao.}
    \begin{center}
    \begin{tabular}{|c|c|c|c|c|c|c|}
        \hline
        0 & 0 & 0 & 0 & 0 & 0 & 0 \\ \hline
        0 & 0 & 0 & 1 & 1 & 1 & 1 \\ \hline
        0 & 1 & 1 & 0 & 0 & 1 & 1 \\ \hline
        0 & 1 & 1 & 1 & 1 & 0 & 0 \\ \hline
        1 & 0 & 1 & 0 & 1 & 0 & 1 \\ \hline
        1 & 0 & 1 & 1 & 0 & 1 & 0 \\ \hline
        1 & 1 & 0 & 0 & 1 & 1 & 0 \\ \hline
        1 & 1 & 0 & 1 & 0 & 0 & 1 \\ \hline
    \end{tabular}
    \end{center}
\end{frame}

\end{document}
