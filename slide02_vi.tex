\documentclass{beamer}
\usetheme{default}
\usepackage[utf8]{inputenc}
\usepackage[T5]{fontenc}
\usepackage[vietnamese]{babel}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{tikz}
\usetikzlibrary{shapes, arrows}

\title{<PERSON><PERSON><PERSON> thử Phần mềm \\ [2] Tổng quan về <PERSON> thuật Phần mềm}
\author{AMAN Hirohisa \\ \texttt{<EMAIL>}}
\date{}

\begin{document}

% Trang 1
\begin{frame}
    \titlepage
\end{frame}

% Trang 2
\begin{frame}{Vai trò của Phần mềm}
    \begin{itemize}
        \item Hệ thống máy tính là sự kết hợp giữa phần cứng và phần mềm.
    \end{itemize}
    \vfill
    \begin{columns}[T]
        \begin{column}{.3\textwidth}
            \begin{beamercolorbox}[sep=0.5em,center]{beamercolorbox}
                \textbf{<PERSON><PERSON> thống Má<PERSON> t<PERSON>}
                \begin{itemize}
                    \item <PERSON><PERSON>n mềm
                    \item <PERSON><PERSON>n cứng
                \end{itemize}
            \end{beamercolorbox}
        \end{column}
        \begin{column}{.7\textwidth}
            \begin{block}{Vai trò của phần mềm là làm chủ phần cứng}
                Cách thức sử dụng phần cứng phụ thuộc vào phần mềm.
            \end{block}
        \end{column}
    \end{columns}
    \vfill
    \begin{itemize}
        \item Game là một ví dụ điển hình.
    \end{itemize}
\end{frame}

% Trang 3
\begin{frame}{(Tham khảo) Các Console Game Gia đình Chính đến năm 2000}
    \begin{columns}
        \begin{column}{.7\textwidth}
            \tiny
            \begin{tabular}{|l|c|c|c|}
                \hline
                \textbf{Tên} & \textbf{CPU} & \textbf{Bộ nhớ chính} & \textbf{Năm} \\ \hline
                Family Computer & 8-bit & 2KB & 1983 \\
                Super Famicom & 16-bit & 128KB & 1990 \\
                PlayStation & 32-bit & 2MB & 1994 \\
                Sega Saturn & 32-bit & 2MB & 1994 \\
                NINTENDO64 & 64-bit & 4.5MB & 1996 \\
                Dreamcast & 32-bit & 16MB & 1998 \\
                PlayStation2 & 128-bit & 32MB & 2000 \\ \hline
            \end{tabular}
        \end{column}
        \begin{column}{.3\textwidth}
            \begin{beamercolorbox}[sep=0.5em,center]{beamercolorbox}
                \textbf{Yêu cầu Phần mềm} \\
                Chất lượng cao \\
                \& Độ tin cậy cao \\
                \& Giá thành thấp
            \end{beamercolorbox}
        \end{column}
    \end{columns}
    \begin{itemize}
        \item Phần mềm được thiết kế để khai thác tối đa tiềm năng của phần cứng cho những trò chơi hấp dẫn.
        \item Mặt khác, việc sửa lỗi sau khi phát hành là cực kỳ khó khăn (do phân phối trên băng cassette hoặc CD/DVD). \rightarrow \textbf{Kiểm thử là rất quan trọng}.
    \end{itemize}
\end{frame}

% Trang 4
\begin{frame}{Kỹ thuật Phần mềm}
    \begin{block}{Một ngành khoa học để phát triển phần mềm chất lượng cao một cách hiệu quả}
        \begin{itemize}
            \item Đây \textbf{không} phải là cách tiếp cận "một vài anh hùng làm việc chăm chỉ sẽ xoay xở được".
            \item Đây là một ngành khoa học để làm cho việc phát triển thành công.
        \end{itemize}
    \end{block}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{Lý thuyết \& Kỹ thuật cho Quy trình}
                Để thực hiện hiệu quả:
                \begin{itemize}
                    \item Phân tích Yêu cầu
                    \item Thiết kế
                    \item Lập trình
                    \item Kiểm thử, v.v.
                \end{itemize}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{Lý thuyết \& Kỹ thuật cho Quản lý}
                Để quản lý thành công các hoạt động phát triển (Quản lý Phát triển).
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Trang 5
\begin{frame}{Chuyện bên lề... Câu chuyện về Man-Month}
    \begin{block}{Man-Month = Số người × Số tháng}
        Một đơn vị nỗ lực (lao động) cho phát triển.
        \begin{itemize}
            \item Ví dụ: 6 man-month = 3 kỹ sư trong 2 tháng.
        \end{itemize}
    \end{block}

    \begin{alertblock}{Kịch bản "Quản lý tồi"}
        \textbf{Tình huống:} "Còn lại 10 man-month công việc! Nhưng thời hạn chỉ còn nửa tháng (0.5 tháng)." \\
        \textbf{Quản lý tồi:} "Được rồi, hãy thêm 20 người." (Vì 10 / 0.5 = 20) \\
        \textbf{Kết quả:} Dự án rơi vào hỗn loạn!
    \end{alertblock}

    \begin{itemize}
        \item Một quy trình phát triển có tổ chức là rất quan trọng.
        \item (Nhân tiện, ước lượng nỗ lực là một lĩnh vực con của kỹ thuật phần mềm.)
    \end{itemize}
\end{frame}

% Trang 6
\begin{frame}{Trách nhiệm lớn của Phần mềm}
    \begin{block}{Chìa khóa cho An toàn và Bảo mật trong Cuộc sống Hàng ngày}
        \begin{center}
            \textbf{Độ tin cậy của Phần mềm}
        \end{center}
    \end{block}
    \begin{itemize}
        \item Lỗi phần mềm có thể gây ra những hỏng hóc nghiêm trọng.
        \begin{itemize}
            \item Có thể ảnh hưởng đến điện, gas, nước, giao thông, tài chính.
            \item (Ví dụ) Dừng thị trường chứng khoán \rightarrow Tổn thất tài chính nặng nề.
        \end{itemize}
        \item Có thể gây ra tai nạn.
        \begin{itemize}
            \item (Ví dụ) Thang máy bị trục trặc \rightarrow Tai nạn chết người.
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{Cần thiết phải kiểm thử trong nhiều tình huống khác nhau.}
    \end{alertblock}
\end{frame}

% Trang 7
\begin{frame}{Đặc điểm của Phần mềm (1/4)}
    \begin{block}{Khó nắm bắt Trạng thái Thực tế}
        \begin{itemize}
            \item Phần mềm là một thực thể logic, không phải vật lý.
            \item Nói cách khác, nó không tồn tại dưới dạng một "vật thể" vật lý.
        \end{itemize}
        \vfill
        Rất khó nắm bắt những điều như:
        \begin{itemize}
            \item Việc phát triển đã tiến triển đến đâu?
            \item Nó có được xây dựng theo đặc tả (tiêu chuẩn dự định) không?
        \end{itemize}
        \begin{flushright}
            \tiny Trái lại, trong xây dựng, tiến độ công việc có thể được nắm bắt sơ bộ từ bên ngoài.
        \end{flushright}
    \end{block}
\end{frame}

% Trang 8
\begin{frame}{Đặc điểm của Phần mềm (2/4)}
    \begin{block}{Công việc tập trung trong Quy trình Phát triển}
        \begin{itemize}
            \item Đối với phần cứng, quy trình là phát triển sản phẩm rồi sau đó sản xuất trong nhà máy.
            \item Đối với phần mềm, sản xuất chỉ đơn giản là thao tác sao chép.
        \end{itemize}
        \vfill
        \begin{itemize}
            \item Cả chất lượng và chi phí đều tập trung vào quy trình phát triển.
            \item \textbf{Phần cứng}: Lo lắng về việc tạo ra lỗi trong quá trình sản xuất.
            \item \textbf{Phần mềm}: Sản phẩm lỗi có nghĩa là "toàn bộ đã có vấn đề từ đầu".
            \item[$\rightarrow$] \alert{Mọi thứ đều phụ thuộc vào việc không thất bại trong phát triển.}
        \end{itemize}
        \begin{flushright}
            \tiny Các trò chơi được đề cập ở đầu là một ví dụ điển hình.
        \end{flushright}
    \end{block}
\end{frame}

% Trang 9
\begin{frame}{Đặc điểm của Phần mềm (3/4)}
    \begin{block}{Thời gian Vận hành và Bảo trì dài}
        Thời gian cho việc:
        \begin{itemize}
            \item \textbf{Vận hành} phần mềm (khi người dùng thực sự sử dụng nó)
            \item \textbf{Bảo trì} (thực hiện các sửa đổi, cải tiến, mở rộng)
        \end{itemize}
        dài hơn nhiều so với thời gian tạo ra nó.
        \vfill
        \begin{itemize}
            \item Không có sự hao mòn vật lý, và một khi đã sử dụng, nó thường trở thành "mối quan hệ lâu dài".
        \end{itemize}
        \begin{alertblock}{
            Tính dễ bảo trì trở thành một điểm chính.
        }
        \end{alertblock}
    \end{block}
\end{frame}

% Trang 10
\begin{frame}{Tầm quan trọng của Khả năng Bảo trì}
    \begin{block}{}
        Ví dụ, giả sử vận hành và bảo trì kéo dài 20 năm.
        \begin{itemize}
            \item Những người tạo ra nó sẽ không chăm sóc nó mãi mãi.
            \item[$\rightarrow$] Cuối cùng, những người khác sẽ tiếp quản việc bảo trì.
            \item Do đó, nó nên được tạo thành phần mềm có khả năng bảo trì cao.
        \end{itemize}
    \end{block}
    \begin{block}{"Hệ thống Kế thừa"}
        Có nhiều hệ thống trên thế giới, được gọi là "hệ thống kế thừa," đã được sử dụng trong 20 hoặc 30 năm.
        \begin{itemize}
            \item Ý tưởng "đừng động vào những gì hiện đang hoạt động" đã ăn sâu vào lĩnh vực này.
        \end{itemize}
        \begin{flushright}
            \tiny (Ví dụ) Hệ thống tài chính được viết bằng COBOL.
        \end{flushright}
    \end{block}
\end{frame}

% Trang 11
\begin{frame}{Đặc điểm của Phần mềm (4/4)}
    \begin{block}{Ít Tái sử dụng}
        \begin{itemize}
            \item \textbf{Phần cứng}: Việc tái sử dụng (chuyển đổi mục đích) các bộ phận hiện có là phổ biến.
            \item \textbf{Phần mềm}: Khó hoàn thành chỉ bằng cách kết hợp các bộ phận (cần nhiều tùy chỉnh khác nhau).
        \end{itemize}
        \vfill
        \begin{itemize}
            \item Mặc dù phần mềm có thể tái sử dụng tồn tại dưới dạng thư viện, nhưng không thể sử dụng mà không lập trình.
        \end{itemize}
    \end{block}
\end{frame}

% Trang 12
\begin{frame}{Tái sử dụng}
    \begin{block}{Tái sử dụng phần mềm được chia rộng ra thành hai loại}
        \begin{itemize}
            \item \textbf{Tái sử dụng Hộp đen}
            \begin{itemize}
                \item Sử dụng nó như một thành phần, mà không cần quan tâm đến bên trong.
            \end{itemize}
            \vfill
            \item \textbf{Tái sử dụng Hộp trắng}
            \begin{itemize}
                \item "Sao chép và dán" chương trình, và viết lại khi cần thiết.
            \end{itemize}
        \end{itemize}
    \end{block}
\end{frame}

% Trang 13
\begin{frame}{Tái sử dụng Hộp đen}
    \begin{block}{Dưới dạng thư viện}
        Ví dụ, sử dụng các hàm hiện có như chúng vốn có, mà không quan tâm đến nội dung mã nguồn. Nó chỉ có thể được sử dụng theo cách đã định trước (theo hướng dẫn), nhưng rất tiện lợi.
        \begin{block}{Mô tả sơ đồ}
            Sơ đồ minh họa khái niệm tái sử dụng hộp đen. Nó hiển thị một hộp được gán nhãn "Mã hiện có" chứa các ví dụ như hàm `scanf` và `printf`. Một mũi tên chỉ từ hộp này đến một hộp mới được gán nhãn "Mã mới", với nhãn `#include <xxxx.h>` trên mũi tên. Điều này biểu thị rằng mã mới đang kết hợp mã hiện có như một thư viện hoặc mô-đun mà không cần biết chi tiết bên trong, chỉ cần biết cách sử dụng nó thông qua giao diện công khai.
        \end{block}
        (Ví dụ) Hàm `scanf` và `printf` trong ngôn ngữ C.
    \end{block}
\end{frame}

% Trang 14
\begin{frame}{Tái sử dụng Hộp trắng}
    \begin{block}{Dưới dạng "sao chép và dán" mã nguồn}
        Ví dụ, tái sử dụng một phần của mã nguồn hiện có. Chép lại mã được xuất bản trong sách hoặc trên trang web.
        \begin{itemize}
            \item Khi tạo mã sử dụng "mã tốt" làm mô hình, điều này là phù hợp.
            \item Tuy nhiên, cũng có nguy cơ sao chép sai lầm (bug) hoặc sử dụng sai.
            \begin{flushright}
                \tiny (Ví dụ) Sao chép-dán một chương trình bạn không hiểu chỉ dẫn đến nhầm lẫn.
            \end{flushright}
        \end{itemize}
        \begin{block}{Mô tả sơ đồ}
            Sơ đồ giải thích tái sử dụng hộp trắng. Nó mô tả hai khối mã, "Mã hiện có" và "Mã mới". Một đoạn mã từ khối "Mã hiện có" được tô sáng và một mũi tên có nhãn "Sao chép" chỉ từ nó đến khối "Mã mới", nơi đoạn được sao chép được tích hợp. Điều này hình dung quá trình sao chép và dán một đoạn mã từ nguồn này sang nguồn khác để tái sử dụng, với hàm ý rằng hoạt động bên trong của mã có thể nhìn thấy và có thể sửa đổi.
        \end{block}
    \end{block}
\end{frame}

% Trang 15
\begin{frame}{Bài tập 1: Xem xét các vấn đề của bản sao mã}
    \begin{block}{}
        Thứ gì đó là bản sao trực tiếp copy-paste của mã nguồn, hoặc tái sử dụng có sửa đổi một phần của nó, được gọi là "Bản sao Mã".
    \end{block}
    \begin{block}{Trong ngành công nghiệp phần mềm, phần mềm có nhiều bản sao mã được coi là có vấn đề.}
        Tại sao lại như vậy?
    \end{block}
\end{frame}

% Trang 16
\begin{frame}{Bài tập 1 (Giải thích: Từ góc độ chi phí và rủi ro sửa đổi)}
    \begin{block}{}
        Giả sử một phần của mã (vài dòng) đã được sao chép và dán vào 50 vị trí.
        \begin{itemize}
            \item Thường xảy ra vấn đề trong quá trình vận hành hoặc yêu cầu cải tiến chức năng phát sinh, làm cho việc sửa đổi mã trở nên cần thiết.
            \item[$\checkmark$] Nếu điểm sửa đổi là bản sao mã, tất cả 50 vị trí khác cũng phải được sửa đổi (không được phép bỏ sót).
        \end{itemize}
        \begin{flushright}
            \tiny Nếu nó đã được tạo thành "hàm" từ đầu, việc sửa đổi chỉ ở một nơi...
        \end{flushright}
    \end{block}
\end{frame}

% Trang 17
\begin{frame}{Phần mềm Tốt là gì?}
    \begin{block}{Hãy nhận thức rằng khái niệm khác nhau tùy thuộc vào quan điểm}
        \begin{columns}
            \begin{column}{.4\textwidth}
                \begin{center}
                    (Biểu tượng của một người đại diện cho người dùng) \\
                    \textbf{Người dùng hoặc Khách hàng}
                \end{center}
                \begin{itemize}
                    \item Dễ sử dụng
                    \item Đáp ứng yêu cầu
                \end{itemize}
            \end{column}
            \begin{column}{.2\textwidth}
                \begin{center}
                    \large
                    \textbf{Phần mềm Tốt}
                \end{center}
            \end{column}
            \begin{column}{.4\textwidth}
                \begin{center}
                    (Biểu tượng của một người đại diện cho nhà phát triển) \\
                    \textbf{Nhà phát triển}
                \end{center}
                \begin{itemize}
                    \item Dễ hiểu/bảo trì
                    \item Chi phí/thời gian phù hợp
                \end{itemize}
            \end{column}
        \end{columns}
        \begin{center}
            Kỹ thuật phần mềm chủ yếu tập trung vào quan điểm của nhà phát triển.
        \end{center}
    \end{block}
\end{frame}

% Trang 18
\begin{frame}{Quan điểm của Người dùng (1): Đáp ứng Yêu cầu}
    \begin{block}{Thỏa mãn các yêu cầu từ khách hàng (client)}
        Yêu cầu được phân loại rộng rãi thành hai loại:
        \begin{itemize}
            \item \textbf{Yêu cầu Chức năng}: Các chức năng nên được thực hiện.
            \begin{itemize}
                \item (Ví dụ) Hệ thống phải có khả năng xác thực người dùng. (*"Làm thế nào" là vấn đề riêng*)
            \end{itemize}
            \item \textbf{Yêu cầu Phi chức năng}: "Làm thế nào" các yêu cầu nên được thực hiện.
            \begin{itemize}
                \item (Ví dụ) Xác thực cho 500 người dùng phải hoàn thành trong vòng 3 giây.
            \end{itemize}
            \begin{flushright}
                \tiny Không phải về chính chức năng, mà là cách thực hiện nó, như hiệu suất và bảo mật.
            \end{flushright}
        \end{itemize}
    \end{block}
\end{frame}

% Trang 19
\begin{frame}{Quan điểm của Người dùng (2): Khả năng Sử dụng}
    \begin{block}{Nói một cách, "dễ sử dụng"}
        \begin{itemize}
            \item Thao tác dễ thực hiện.
            \item Thao tác dễ hiểu.
        \end{itemize}
    \end{block}
    \begin{block}{}
        Gián tiếp, tốc độ hoạt động và sử dụng bộ nhớ cũng có liên quan (người dùng không muốn sử dụng hệ thống được gọi là "nặng").
    \end{block}
\end{frame}

% Trang 20
\begin{frame}{Quan điểm của Nhà phát triển (1): Chi phí và Thời gian}
    \begin{block}{Chi phí phát triển nên thấp}
    \end{block}
    \begin{block}{Thời gian phát triển phải trong thời hạn}
        \begin{itemize}
            \item Hiếm khi có thể phát triển chậm rãi theo thời gian.
            \item Cần thiết phải hoàn thành trong thời hạn với nhân lực và thời gian hạn chế.
        \end{itemize}
    \end{block}
    \begin{alertblock}{
        Mục tiêu là phát triển phần mềm có thể giao hàng với chi phí thấp, trong thời hạn, và vẫn đáp ứng các đặc tả yêu cầu.
    }
    \end{alertblock}
\end{frame}

% Trang 21
\begin{frame}{Quan điểm của Nhà phát triển (2): Khả năng Bảo trì}
    \begin{block}{Một khi hoàn thành và đang vận hành, yêu cầu thay đổi gần như luôn luôn}
        \begin{itemize}
            \item Yêu cầu sửa đổi nhỏ: "Tôi muốn phần này được thay đổi như thế này."
            \item Yêu cầu thêm/mở rộng chức năng: "Tôi muốn thêm chức năng như thế này."
        \end{itemize}
    \end{block}
    \begin{block}{Lỗi (bug) cũng có thể được tìm thấy}
    \end{block}
    \begin{alertblock}{
        Phần mềm dễ bảo trì là phần mềm "tốt". Vì những người khác có thể bảo trì nó, "tính dễ hiểu" cũng quan trọng.
    }
    \end{alertblock}
\end{frame}

% Trang 22
\begin{frame}{(Tham khảo) Từ viết tắt 3 chữ cái trong Phát triển Phần mềm}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{QCD}
                \begin{itemize}
                    \item Quality (Chất lượng)
                    \item Cost (Chi phí)
                    \item Delivery (Giao hàng)
                \end{itemize}
                \begin{center}
                    Đại diện cho ba trụ cột quan trọng trong ngành sản xuất.
                \end{center}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{KKD}
                \begin{itemize}
                    \item Kan (勘): Trực giác
                    \item Keiken (経験): Kinh nghiệm
                    \item Dokyo (度胸): Can đảm
                \end{itemize}
                \begin{center}
                    Đại diện cho một địa điểm không được quản lý tốt.
                \end{center}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Trang 23
\begin{frame}{Nghỉ giải lao 10 phút}
    \begin{center}
        \Huge Nghỉ 10 phút
    \end{center}
\end{frame}

% Trang 24
\begin{frame}{Phân loại Phần mềm}
    \begin{center}
        \begin{tikzpicture}[node distance=1.5cm, auto]
            \node (user) {Những gì người dùng muốn làm};
            \node (app) [below of=user, draw, rectangle, minimum height=1cm, minimum width=3cm] {Phần mềm Ứng dụng};
            \node (middle) [below of=app, draw, rectangle, minimum height=1cm, minimum width=3cm] {Middleware};
            \node (os) [below of=middle, draw, rectangle, minimum height=1cm, minimum width=3cm] {Phần mềm Cơ bản};
            \node (computer) [below of=os] {Những gì máy tính có thể làm};
            \draw[<->] (user) -- (app);
            \draw[<->] (os) -- (computer);
        \end{tikzpicture}
    \end{center}
    \begin{block}{Phần mềm Nhúng}
        Hình ảnh của 3 lớp này được tích hợp vào phần cứng.
    \end{block}
\end{frame}

% Trang 25
\begin{frame}{Phần mềm Cơ bản}
    \begin{block}{Phần mềm nền tảng để sử dụng máy tính hoặc chạy phần mềm}
        \begin{itemize}
            \item \textbf{OS (Hệ điều hành)} $\leftarrow$ Đây thường là những gì "Phần mềm Cơ bản" đề cập đến.
            \item Compiler / Interpreter
            \item Chương trình Dịch vụ (Tiện ích)
            \begin{itemize}
                \item (Ví dụ) Chỉnh sửa/tìm kiếm tập tin
            \end{itemize}
        \end{itemize}
    \end{block}
    \begin{alertblock}{
        Phần mềm cơ bản không thực hiện bất kỳ tác vụ kinh doanh cụ thể nào, nhưng không có nó, bạn không thể bắt đầu.
    }
    \end{alertblock}
    \tiny (Ví dụ) Chỉ vì có OS (như Windows) được cài đặt không có nghĩa là bạn có thể làm bất cứ điều gì. Nhưng nếu nó không được cài đặt, không có gì hoạt động.
\end{frame}

% Trang 26
\begin{frame}{Phần mềm Ứng dụng (tức là, "Apps")}
    \begin{block}{Phần mềm có mục đích cụ thể}
        Ví dụ:
        \begin{itemize}
            \item Quản lý Giao dịch Chứng khoán (Phần mềm dành riêng cho ngành)
            \item Quản lý Ngân sách (Phần mềm dành riêng cho doanh nghiệp)
            \item Bảng tính, Thuyết trình (Phần mềm ứng dụng thông dụng)
        \end{itemize}
    \end{block}
    \begin{block}{Phần mềm trở thành công cụ để hoàn thành một số loại công việc hoặc mục đích}
    \end{block}
\end{frame}

% Trang 27
\begin{frame}{Middleware}
    \begin{block}{Trung gian giữa phần mềm cơ bản và phần mềm ứng dụng}
        \begin{itemize}
            \item Một khái niệm tương đối mới, đây là phần mềm tạo nền tảng cho phần mềm ứng dụng.
            \item Ví dụ:
            \begin{itemize}
                \item Hệ thống Quản lý Cơ sở dữ liệu
                \item Web Server
                \item Application Server
            \end{itemize}
        \end{itemize}
        \begin{flushright}
            Phần mềm "cho ứng dụng" hơn là phần mềm người dùng sử dụng trực tiếp.
        \end{flushright}
    \end{block}
    \begin{block}{
        Thay vì sử dụng các chức năng OS trực tiếp (thông qua system call, v.v.), nó được sử dụng ở khái niệm cấp cao hơn: ví dụ, với SQL.
    }
    \end{block}
\end{frame}

% Trang 28
\begin{frame}{Vị trí của Middleware}
    \begin{block}{Ví dụ, giả sử có một ứng dụng trên Windows xử lý một lượng lớn dữ liệu}
        \begin{center}
            \begin{tikzpicture}[node distance=1.5cm, auto, >=stealth]
                \node (app) [draw, rectangle, minimum height=1.5cm, minimum width=4cm] {Phần mềm Ứng dụng};
                \node (middle) [below of=app, yshift=-0.5cm, draw, rectangle, minimum height=1cm, minimum width=3cm] {Middleware};
                \node (db) [right of=middle, xshift=2cm, draw, circle, minimum size=1.5cm] {Cơ sở dữ liệu};
                \node (os) [below of=middle, yshift=-0.5cm, draw, rectangle, minimum height=1cm, minimum width=4cm] {Windows (OS)};

                \draw[->] (app) -- node[right, text width=4cm, midway] {Tìm kiếm và quản lý dữ liệu không được thực hiện trong ứng dụng, mà được yêu cầu từ cơ sở dữ liệu (sử dụng SQL).} (middle);
                \draw[->] (middle) -- (db);
                \draw[->] (db) -- node[left, midway] {Trả về kết quả} (app);
                \draw (app) -- (os);
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Trang 29
\begin{frame}{Phần mềm Nhúng}
    \begin{block}{Phần mềm chủ yếu để điều khiển máy móc trong các thiết bị điện tử thực hiện các chức năng cụ thể}
        (*Không chạy trên máy tính đa mục đích, mà được tích hợp vào các thiết bị cụ thể*)
        \begin{itemize}
            \item Ví dụ: Thiết bị gia dụng, hệ thống định vị xe hơi, thang máy, v.v.
        \end{itemize}
    \end{block}
    \begin{block}{Được cung cấp tích hợp với phần cứng}
        \begin{itemize}
            \item Không dễ dàng cài đặt phiên bản đã sửa đổi.
            \item Môi trường hoạt động đa dạng, và đòi hỏi độ tin cậy cao.
        \end{itemize}
    \end{block}
\end{frame}

% Trang 30
\begin{frame}{Bài tập 2: Xác định các thách thức trong phát triển và bảo trì phần mềm nhúng}
    \begin{block}{Xem xét phần mềm nhúng để điều khiển thang máy}
        \begin{itemize}
            \item Khi phát triển phần mềm này, điều gì khó khăn hơn so với phần mềm thông thường?
            \item Ngoài ra, còn gì về khi bảo trì nó?
        \end{itemize}
    \end{block}
\end{frame}

% Trang 31
\begin{frame}{Bài tập 2 (Câu trả lời mẫu)}
    \begin{columns}
        \begin{column}{.5\textwidth}
            \begin{block}{Thách thức Phát triển}
                \begin{itemize}
                    \item \textbf{Cần tối đa hóa độ tin cậy}
                    \begin{itemize}
                        \item Hành vi mất kiểm soát không thể chấp nhận được.
                        \item Hoạt động an toàn và ổn định luôn được yêu cầu.
                    \end{itemize}
                    \item \textbf{Ảnh hưởng đáng kể của phần cứng và môi trường}
                    \begin{itemize}
                        \item Nhiệt độ và độ ẩm cao vào mùa hè.
                        \item Hao mòn và hỏng hóc vật lý cũng xảy ra.
                    \end{itemize}
                \end{itemize}
            \end{block}
        \end{column}
        \begin{column}{.5\textwidth}
            \begin{block}{Thách thức Bảo trì}
                \begin{itemize}
                    \item \textbf{Sửa đổi không dễ dàng}
                    \begin{itemize}
                        \item Thang máy phải được dừng lại.
                        \item Thay thế phần cứng khó khăn (ví dụ, thêm CPU hoặc bộ nhớ không đơn giản).
                    \end{itemize}
                    \item \textbf{Thời gian hoạt động dài}
                    \begin{itemize}
                        \item 10 đến 20 năm là phổ biến.
                    \end{itemize}
                \end{itemize}
            \end{block}
        \end{column}
    \end{columns}
\end{frame}

% Trang 32
\begin{frame}{Vòng đời}
    \begin{block}{Vòng đời: Toàn bộ cuộc đời của một phần mềm}
        \begin{center}
            \begin{tikzpicture}[node distance=1cm, auto, >=stealth, every node/.style={draw, rectangle, align=center}]
                \node (plan) {Kế hoạch Phát triển};
                \node (dev) [below of=plan, fill=yellow!20] {(Phát triển)};
                \node (req) [below of=dev] {Phân tích Yêu cầu};
                \node (ext) [below of=req] {Thiết kế Bên ngoài};
                \node (int) [below of=ext] {Thiết kế Bên trong};
                \node (impl) [below of=int] {Thực hiện \\ (Lập trình)};
                \node (test) [below of=impl] {Kiểm thử};
                \node (op) [right of=impl, xshift=3cm, fill=green!20] {Vận hành \& Bảo trì};
                \node (disc) [right of=op, xshift=3cm] {Loại bỏ};

                \draw[->] (plan) -- (req);
                \draw[->] (req) -- (ext);
                \draw[->] (ext) -- (int);
                \draw[->] (int) -- (impl);
                \draw[->] (impl) -- (test);
                \draw[->] (test) -- (op);
                \draw[->] (op) -- (disc);
                \draw[<->,dashed] (op) to [out=120,in=60] node[above,draw=none] {Thường, đây là phần dài nhất} (test);
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Trang 33
\begin{frame}{Khủng hoảng Phần mềm}
    \begin{block}{Cảm giác khủng hoảng được ủng hộ vào năm 1968}
        \begin{alertblock}{}
            Phát triển phần mềm không thể theo kịp nhu cầu, cản trở sự phát triển của hệ thống máy tính.
        \end{alertblock}
        \begin{itemize}
            \item Phát triển phần mềm không thể theo kịp tốc độ, cản trở tiến bộ máy tính.
            \item Quy mô tăng $\rightarrow$ Sự gia tăng của bug $\rightarrow$ Phát triển thành vấn đề xã hội.
            \item Tăng chi phí phát triển.
        \end{itemize}
        \begin{center}
            \textbf{Nói tóm lại, phần mềm trở thành nút thắt cổ chai!}
        \end{center}
    \end{block}
\end{frame}

% Trang 34
\begin{frame}{(1) Cản trở Tiến bộ của Máy tính}
    \begin{block}{Hướng đi là "làm cho máy tính làm nhiều thứ khác nhau" (vào thời điểm đó)}
        \begin{center}
            Hãy sản xuất hàng loạt phần cứng đa mục đích với giá rẻ, và để phần mềm xử lý các phản hồi chi tiết.
        \end{center}
        \begin{alertblock}{Nhưng vì phần mềm về cơ bản là "làm thủ công":}
            \begin{itemize}
                \item Phát triển không thể theo kịp!
                \item Không có đủ kỹ sư!
            \end{itemize}
        \end{alertblock}
    \end{block}
\end{frame}

% Trang 35
\begin{frame}{(2) Nỗi sợ Phát triển thành Vấn đề Xã hội}
    \begin{block}{Khi nhu cầu về hệ thống tăng, phần mềm trở nên lớn hơn và phức tạp hơn}
        \begin{itemize}
            \item Tự nhiên, nguy cơ lỗi của con người (được gọi là bug) cũng tăng.
            \begin{alertblock}{}
                Tùy thuộc vào hệ thống và loại bug, nó có thể gây ra thiệt hại nghiêm trọng cho đời sống xã hội.
                \begin{center}
                Có thể ảnh hưởng đến điện, gas, nước, giao thông, tài chính, v.v.
                \end{center}
            \end{alertblock}
        \end{itemize}
    \end{block}
\end{frame}

% Trang 36
\begin{frame}{(3) Tăng Chi phí}
    \begin{block}{Chi phí phát triển và bảo trì phần mềm đang gia tăng}
        \begin{center}
            \begin{tikzpicture}
                \draw[->] (0,0) -- (6,0) node[right] {Thời gian};
                \draw[->] (0,0) -- (0,4) node[above] {Chi phí};
                \draw[blue, thick, ->] (0.5,3) .. controls (2,1) and (4,0.5) .. (5.5,0.8) node[above, text=blue, midway, text width=2cm, align=center] {\textbf{Phần cứng} \\ Lợi ích của tiến bộ công nghệ và sản xuất hàng loạt};
                \draw[red, thick, ->] (0.5,1) .. controls (2,1.5) and (4,3) .. (5.5,3.5) node[above, text=red, midway, text width=2cm, align=center] {\textbf{Phần mềm} \\ Phản hồi thủ công cho yêu cầu và quy mô ngày càng tăng};
            \end{tikzpicture}
        \end{center}
    \end{block}
\end{frame}

% Trang 37
\begin{frame}{Sự ra đời của "Kỹ thuật Phần mềm"}
    \begin{block}{Cần có biện pháp đối phó để giải quyết những vấn đề như vậy}
        Nghiên cứu lý thuyết và kỹ thuật để phát triển phần mềm chất lượng cao một cách hiệu quả (cho đến lúc đó, nó gần với "thủ công nghệ" hơn).
    \end{block}
    \begin{block}{Thiết lập và hệ thống hóa lý thuyết và kỹ thuật phát triển phần mềm như một ngành "Kỹ thuật" (nghiên cứu về việc tạo ra các thứ)}
        \begin{center}
            \alert{Năng suất cao và chất lượng cao!}
        \end{center}
    \end{block}
\end{frame}

% Trang 38
\begin{frame}{Thách thức Hiện tại và Tương lai}
    \begin{block}{Ngành công nghiệp phần mềm đã phát triển thông qua nhiều nghiên cứu và đổi mới công nghệ, nhưng những thách thức khó khăn vẫn còn}
        \begin{itemize}
            \item \alert{Khó khăn trong phân tích yêu cầu}
            \item \alert{Khó khăn trong tái sử dụng}
            \item \alert{Khó khăn trong quản lý dự án}
            \item \alert{Khó khăn trong ước lượng}
        \end{itemize}
    \end{block}
\end{frame}

% Trang 39
\begin{frame}{Khó khăn trong Phân tích Yêu cầu}
    \begin{block}{Phân tích yêu cầu của khách hàng và mô tả chúng như các đặc tả hợp lệ không có sự mơ hồ cho mục đích phát triển.}
        \begin{center}
            \textit{Bước đầu tiên, nhưng tập trung vào con người nhất và do đó đầy khó khăn.}
        \end{center}
        \begin{columns}
            \begin{column}{.5\textwidth}
                Nếu khách hàng là người nghiệp dư về phần mềm, họ có thể đưa ra những yêu cầu không hợp lý một cách đáng ngạc nhiên.
                \begin{center}
                Loại cuộc trò chuyện "Bạn không thể làm điều đó".
                \end{center}
            \end{column}
            \begin{column}{.5\textwidth}
                Nếu lĩnh vực (domain) khác nhau, có sự nhầm lẫn do văn hóa và thuật ngữ khác nhau.
                \begin{center}
                Loại cuộc trò chuyện "Chúng tôi không biết điều gì là kiến thức phổ thông đối với họ".
                \end{center}
            \end{column}
        \end{columns}
    \end{block}
\end{frame}

% Trang 40
\begin{frame}{Khó khăn trong Tái sử dụng}
    \begin{block}{Tái sử dụng Mã nguồn}
        \begin{itemize}
            \item Tương đối dễ thực hành, nhưng copy-paste bất cẩn cũng có thể gây bug $\rightarrow$ Bản sao Mã.
            \item Tôi nên sử dụng mã nào?
        \end{itemize}
    \end{block}
    \begin{block}{Tái sử dụng Thiết kế và Kiến trúc}
        \begin{itemize}
            \item Tái sử dụng "kiến thức và bí quyết" ở mức trừu tượng hơn so với mã, vì vậy rào cản khá cao.
            \item Cần kiến thức về mẫu thiết kế, framework.
        \end{itemize}
    \end{block}
\end{frame}

% Trang 41
\begin{frame}{Khó khăn trong Quản lý Dự án}
    \begin{block}{Trong việc quản lý một dự án phát triển, các mục như:}
        \begin{itemize}
            \item Quản lý tiến độ
            \item Quản lý chất lượng
            \item Phân bổ nhân sự, giao tiếp
        \end{itemize}
        là những thách thức cho người quản lý dự án.
    \end{block}
    \begin{block}{}
        Ví dụ, nó có thể kết thúc với chỉ một vài người chạy xung quanh trong sự nhầm lẫn, và phát triển hiệu quả cho toàn bộ nhóm không thể đạt được.
    \end{block}
\end{frame}

% Trang 42
\begin{frame}{Khó khăn trong Ước lượng}
    \begin{block}{Trong thực tế, vẫn còn một yếu tố lớn về tính cách}
        \begin{itemize}
            \item Nói cách khác, nó phần lớn phụ thuộc vào kinh nghiệm và khả năng của từng kỹ sư.
        \end{itemize}
    \end{block}
    \begin{block}{Ước lượng nỗ lực, thời gian và chi phí từ đó, không cần phải nói, là khó khăn $\rightarrow$ Chậm trễ và vượt chi phí}
        \begin{itemize}
            \item Mô hình ước lượng nỗ lực (COCOMO)
            \item Mô hình trưởng thành tổ chức phát triển (CMM, CMMI)
            \item Dự đoán bằng thống kê, mạng nơ-ron, v.v.
        \end{itemize}
    \end{block}
\end{frame}

% Trang 43
\begin{frame}{Tóm tắt}
    \begin{itemize}
        \item Kỹ thuật phần mềm không chỉ về cách tạo phần mềm, mà còn là ngành quản lý để dẫn dắt một dự án phát triển đến thành công.
        \vfill
        \item Nó được sinh ra từ khủng hoảng phần mềm (theo nghĩa đó, mối liên hệ của nó với ngành công nghiệp là mạnh mẽ).
        \vfill
        \item Ngành công nghiệp phần mềm đang phát triển, nhưng vẫn còn nhiều thách thức cần được giải quyết: phân tích yêu cầu, tái sử dụng, quản lý dự án, ước lượng, v.v.
    \end{itemize}
\end{frame}

% Trang 44
\begin{frame}{Bài tập về nhà}
    \begin{center}
        \Huge Trả lời "[02] quiz"
        \Large (trước thứ Sáu 23:59)
    \end{center}
    \vfill
    \begin{block}{Lưu ý}
        Điểm quiz của bạn sẽ là một phần của đánh giá cuối cùng.
    \end{block}
\end{frame}

\end{document}