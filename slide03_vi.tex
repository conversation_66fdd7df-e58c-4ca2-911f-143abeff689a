\documentclass{beamer}
\usepackage[utf8]{inputenc}
\usepackage[vietnamese]{babel}
\usepackage{tikz}
\usetikzlibrary{shapes, arrows, decorations.pathreplacing}

% Define block styles
\tikzstyle{block} = [rectangle, draw, fill=blue!20, 
    text width=8em, text centered, rounded corners, minimum height=4em]
\tikzstyle{line} = [draw, -latex']

\title{Kiểm Thử <PERSON>ầ<PERSON>ề<PERSON> \\ [3] Quy <PERSON>r<PERSON> Phát Triển Phần Mềm}
\author{Hirohisa AMAN \\ \texttt{<EMAIL>}}
\date{}

\begin{document}

\begin{frame}
    \titlepage
\end{frame}

\begin{frame}{Tổng Quan Phát Triển Phần Mềm}
    \begin{tikzpicture}[node distance = 1cm, auto]
        % Place nodes
        \node [block] (planning) {Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)};
        \node [block, below of=planning] (analysis) {<PERSON>ân Tích <PERSON>};
        \node [block, below of=analysis] (design) {<PERSON><PERSON><PERSON><PERSON>};
        \node [block, below of=design] (implementation) {<PERSON>ể<PERSON> (Lập Tr<PERSON>nh)};
        \node [block, below of=implementation] (testing) {Kiểm Thử};
        \node [block, below of=testing] (maintenance) {Vận Hành \& Bảo Trì};
        
        % Draw edges
        \path [line] (planning) -- (analysis);
        \path [line] (analysis) -- (design);
        \path [line] (design) -- (implementation);
        \path [line] (implementation) -- (testing);
        \path [line] (testing) -- (maintenance);
        
        % Brace
        \draw[decorate,decoration={brace,amplitude=10pt,mirror},yshift=-0.2cm]
            (analysis.east) -- (testing.east) node[midway,right,xshift=0.5cm] {Quy Trình Phát Triển};
    \end{tikzpicture}
\end{frame}

\begin{frame}{(0) Lập Kế Hoạch Phát Triển}
    \begin{itemize}
        \item[\Large $\square$] \alert{Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)}
        \item[\Large $\square$] Phân Tích Yêu Cầu
        \item[\Large $\square$] Thiết Kế
        \item[\Large $\square$] Triển Khai (Lập Trình)
        \item[\Large $\square$] Kiểm Thử
        \item[\Large $\square$] Vận Hành \& Bảo Trì
    \end{itemize}
\end{frame}

\begin{frame}{(0) Lập Kế Hoạch Phát Triển}
    \centering
    \begin{tikzpicture}[node distance=1.5cm]
        \node (setting) [block, text width=10em] {Thiết Lập: Xác định cần tạo gì và khi nào.};
        \node (estimation) [block, right of=setting, xshift=4cm, text width=7em] {Ước Lượng: Ước lượng nỗ lực và chi phí.};
        \node (preparation) [block, below of=estimation, yshift=-1cm, text width=10em] {Chuẩn Bị Dự Án Phát Triển:
            \begin{itemize}
                \item Phân công nhân sự
                \item Biện pháp ngân sách
                \item Sắp xếp môi trường, v.v.
            \end{itemize}
        };
        \node (decision) [block, below of=setting, yshift=-1cm, text width=10em] {Quyết định phương pháp phát triển.};
        \node (note) [rectangle, draw, below of=decision, yshift=-1.5cm, text width=15em] {Vì không phải lúc nào cũng có thể triển khai tất cả các chức năng "mong muốn" trong thời gian và ngân sách bằng chính mình, cũng cần quyết định tạo bao nhiêu.};
        
        \path[line] (setting) -- (estimation);
        \path[line] (estimation) -- (preparation);
        \path[line] (setting) -- (decision);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(0) Lập Kế Hoạch Phát Triển: Quyết Định Phương Pháp Phát Triển}
    \begin{block}{Chính Sách Cơ Bản}
        \begin{itemize}
            \item Phát triển từ đầu?
            \item Dựa trên hệ thống hiện có?
        \end{itemize}
    \end{block}

    \begin{tikzpicture}[node distance=1.5cm, auto]
        \node (scale) [block, text width=20em] {Tính toán quy mô phát triển (ví dụ: cần viết bao nhiêu mã?
)};
        \node (estimation) [block, below of=scale, text width=20em] {Điều này ảnh hưởng đến việc ước lượng nỗ lực và chi phí.};
        \node (issues) [block, below of=estimation, text width=25em] {Các vấn đề khác cần xem xét: Đảm bảo nguồn nhân lực và cấu trúc nhóm? Môi trường phát triển? Ngân sách?};
        
        \path [line] (scale) -- (estimation);
        \path [line] (estimation) -- (issues);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(1) Phân Tích Yêu Cầu}
    \begin{itemize}
        \item[\Large $\square$] Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)
        \item[\Large $\square$] \alert{Phân Tích Yêu Cầu}
        \item[\Large $\square$] Thiết Kế
        \item[\Large $\square$] Triển Khai (Lập Trình)
        \item[\Large $\square$] Kiểm Thử
        \item[\Large $\square$] Vận Hành \& Bảo Trì
    \end{itemize}
    \vfill
    \begin{center}
        \huge \alert{Bắt đầu quy trình phát triển}
    \end{center}
\end{frame}

\begin{frame}{(1) Phân Tích Yêu Cầu}
    \begin{block}{Sản Phẩm Bàn Giao: Đặc Tả Yêu Cầu}
        Làm rõ nội dung cần thực hiện với phần mềm.
    \end{block}
    
    \begin{itemize}
        \item[\Large $\square$] \textbf{Phân Tích Nghiệp Vụ}
        \begin{itemize}
            \item Phân tích và tổ chức các hoạt động nghiệp vụ cần thực hiện bằng phần mềm.
        \end{itemize}
        \item[\Large $\square$] \textbf{Mô Tả Yêu Cầu}
        \begin{itemize}
            \item Mô tả chính xác, không mơ hồ.
            \item Đồng thời, xem xét tính khả thi từ góc độ chức năng, chi phí và thời hạn giao hàng.
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Ví Dụ】Hệ Thống Thông Báo Bảng Tin (1) Phân Tích Yêu Cầu}
    \begin{itemize}
        \item[\Large $\square$] Tôi muốn một hệ thống thông báo cho tôi trên điện thoại thông minh khi có thông báo liên quan được đăng.
        \begin{itemize}
            \item Thời gian thực hay tóm tắt hàng ngày?
            \item Thông báo nên được gửi dưới dạng nào?
            \item Cách đăng ký/thay đổi đích thông báo?
            \begin{itemize}
                \item Biện pháp chống giả mạo và bảo vệ bảo mật thì sao?
            \end{itemize}
            \item Nội dung thông báo nên được nhập theo định dạng nào?
            \item Điều kiện cho "thông báo liên quan" ngay từ đầu là gì?
            \begin{itemize}
                \item Có cần tích hợp với cơ sở dữ liệu sinh viên không?
            \end{itemize}
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Bài Tập 1】Xem xét các mục nghiên cứu cần thiết cho phân tích yêu cầu}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Yêu cầu từ khách hàng:}
        \begin{itemize}
            \item "Tôi muốn bạn tạo một hệ thống cho phép tôi kiểm tra tình trạng đông đúc của căng tin trên điện thoại thông minh."
        \end{itemize}
        \item[\Large $\square$] \textbf{Hãy xem xét và liệt kê các mục cần xác nhận (hoặc nghiên cứu) với khách hàng khi tạo đặc tả yêu cầu.}
    \end{itemize}
\end{frame}

\begin{frame}{(2) Thiết Kế}
    \begin{itemize}
        \item[\Large $\square$] Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)
        \item[\Large $\square$] Phân Tích Yêu Cầu
        \item[\Large $\square$] \alert{Thiết Kế}
        \item[\Large $\square$] Triển Khai (Lập Trình)
        \item[\Large $\square$] Kiểm Thử
        \item[\Large $\square$] Vận Hành \& Bảo Trì
    \end{itemize}
\end{frame}

\begin{frame}{(2-1) Thiết Kế Bên Ngoài}
    \begin{block}{Sản Phẩm Bàn Giao: Đặc Tả Bên Ngoài}
        Chia hệ thống mục tiêu thành các hệ thống con và thiết kế đặc tả của mỗi hệ thống con như nhìn từ bên ngoài.
    \end{block}

    \begin{tikzpicture}[node distance=2cm]
        \node (user) [shape=rectangle, text width=6em, align=center] {Người Dùng hoặc Hệ Thống Bên Ngoài};
        \node (system) [shape=ellipse, draw, fill=blue!20, text width=5em, align=center, right of=user, xshift=1cm] {Hệ Thống Tổng Thể};
        \node (subsystem) [shape=ellipse, draw, fill=orange!30, below of=user, xshift=1cm] {Hệ Thống Con};

        \node (q1) [above of=system, yshift=0.5cm] {Sẽ có những chức năng gì?};
        \node (q2) [right of=system, xshift=0.5cm, align=center] {Sẽ có loại \\ giao diện nào?};
        \node (q3) [below of=q2, align=center] {Dữ liệu và \\ điều khiển sẽ được trao đổi như thế nào?};
        \node (q4) [left of=subsystem, xshift=-2cm] {Sẽ được vận hành như thế nào?};
        
        \path[line] (user) -- (system);
        \path[line] (system) -- (subsystem);
        \path[line] (subsystem) -- (user);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(2-2) Thiết Kế Bên Trong}
    \begin{block}{Sản Phẩm Bàn Giao: Đặc Tả Bên Trong}
        Thiết kế đặc tả chương trình chi tiết cho mỗi thiết kế bên ngoài.
    \end{block}

    \begin{tikzpicture}[node distance=1.5cm]
        \node(subsystem) [shape=ellipse, draw, fill=orange!30] {Hệ Thống Con};
        \node(funcA) [block, right of=subsystem, xshift=2cm] {Chức Năng A};
        \node(funcB) [block, below of=funcA, yshift=-0.5cm, xshift=-1.5cm] {Chức Năng B};
        \node(funcC) [block, right of=funcB, xshift=1cm] {Chức Năng C};
        \node(funcD) [block, below of=funcB] {Chức Năng D};
        \node(funcF) [block, right of=funcD, xshift=1cm] {Chức Năng F};

        \path[line] (subsystem) -| (funcA);
        \path[line] (funcA) -- (funcB);
        \path[line] (funcA) -- (funcC);
        \path[line] (funcB) -- (funcD);
        \path[line] (funcC) -- (funcF);

        \node[rectangle, draw, right of=funcA, xshift=2.5cm, text width=10em, font=\scriptsize] {
            \begin{itemize}
                \item Cấu trúc chương trình (phân công công việc như các chức năng, v.v.)
                \item Đặc tả của mỗi chức năng (I/O, chức năng, v.v.)
                \item Cấu trúc dữ liệu và thuật toán
            \end{itemize}
        };

        \node[below of=funcF, yshift=-1cm, text width=15em, align=center] {\alert{Thiết kế đến một bước trước lập trình}};
        \node[rectangle, draw, below of=subsystem, yshift=-2.5cm, text width=15em] {※ "Chức năng" không chính xác, khái niệm là "Module"};

    \end{tikzpicture}
\end{frame}

\begin{frame}{【Ví Dụ】Hệ Thống Thông Báo Bảng Tin (2-1) Thiết Kế Bên Ngoài}
    \begin{tikzpicture}[node distance=2.5cm, auto,
        actor/.style={
          shape=rectangle,
          path picture={
            \node at (path picture bounding box.center){
                \begin{tikzpicture}
                    \draw (0,0.3) circle (0.2);
                    \draw (0,0.1) -- (0,-0.4);
                    \draw (-0.3,-0.1) -- (0.3,-0.1);
                    \draw (0,-0.4) -- (-0.3,-0.7);
                    \draw (0,-0.4) -- (0.3,-0.7);
                \end{tikzpicture}
            };
          }
        }]

        \node (user) [actor, label=below:Người Dùng] {};
        \node (data_input) [actor, right of=user, xshift=6cm, label=below:Người Nhập Dữ Liệu] {};

        \node (content_mgmt) [block, above right of=user, yshift=1cm] {Quản Lý Nội Dung};
        \node (user_mgmt) [block, right of=content_mgmt] {Quản Lý Người Dùng};
        \node (sending_mgmt) [block, below of=content_mgmt, yshift=-1cm] {Quản Lý Gửi};

        \node (student_db) [shape=cylinder, draw, shape border rotate=90, aspect=0.2, below of=user, yshift=-1cm, label=below:Cơ Sở Dữ Liệu Sinh Viên] {};
        \node (user_db) [shape=cylinder, draw, shape border rotate=90, aspect=0.2, below of=data_input, yshift=-1cm, label=below:Cơ Sở Dữ Liệu Người Dùng] {};

        \path[line] (user) -- (sending_mgmt);
        \path[line] (user) -- (user_mgmt);
        \path[line] (data_input) -- (content_mgmt);
        \path[line] (sending_mgmt) -- (student_db);
        \path[line] (user_mgmt) -- (user_db);
        \path[line] (content_mgmt) -- (sending_mgmt);

        \node[below of=user_db, yshift=-1cm, text width=15em, align=center] {※ Cơ sở dữ liệu không phải là người, nhưng...};
    \end{tikzpicture}
\end{frame}

\begin{frame}{【Ví Dụ】Hệ Thống Thông Báo Bảng Tin (2-2) Thiết Kế Bên Trong}
    \frametitle{Hệ Thống Con Quản Lý Gửi}

    \begin{tikzpicture}[node distance=1.5cm, auto]
        \node (main) [block] {Quản Lý Gửi Chính};
        \node (create_list) [block, below of=main, xshift=-3cm] {Tạo Danh Sách Người Nhận};
        \node (create_content) [block, right of=create_list, xshift=1cm] {Tạo Nội Dung};
        \node (send_message) [block, right of=create_content, xshift=1cm] {Gửi Tin Nhắn};
        \node (search_db) [block, below of=create_list] {Tìm Kiếm Cơ Sở Dữ Liệu Sinh Viên};

        \path[line] (main) -- (create_list);
        \path[line] (main) -- (create_content);
        \path[line] (main) -- (send_message);
        \path[line] (create_list) -- (search_db);

        \node[rectangle, draw, right of=send_message, xshift=2cm, text width=10em, align=left, font=\small] {
            Cấu trúc dữ liệu nào cho danh sách người nhận? \\
            Cách phối hợp với cơ sở dữ liệu? \\
            Cách gửi tin nhắn?
        };
    \end{tikzpicture}
\end{frame}

\begin{frame}{(3) Triển Khai (Lập Trình)}
    \begin{itemize}
        \item[\Large $\square$] Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)
        \item[\Large $\square$] Phân Tích Yêu Cầu
        \item[\Large $\square$] Thiết Kế
        \item[\Large $\square$] \alert{Triển Khai (Lập Trình)}
        \item[\Large $\square$] Kiểm Thử
        \item[\Large $\square$] Vận Hành \& Bảo Trì
    \end{itemize}
\end{frame}

\begin{frame}{(3) Triển Khai (Lập Trình)}
    \begin{block}{Sản Phẩm Bàn Giao: Đặc Tả Chương Trình, Mã Nguồn}
        Viết mã dựa trên đặc tả bên trong.
    \end{block}

    \begin{itemize}
        \item[\Large $\square$] Không cần phải nói, thực hiện viết mã (lập trình) bằng ngôn ngữ lập trình.
        \item[\Large $\square$] Cũng tạo các tài liệu liên quan như đặc tả chương trình và sơ đồ luồng.
    \end{itemize}
\end{frame}

\begin{frame}{【Ví Dụ】Hệ Thống Thông Báo Bảng Tin (3) Triển Khai (Lập Trình)}
    \begin{itemize}
        \item[\Large $\square$] Sử dụng thư viện và ngôn ngữ lập trình phù hợp để thực hiện các chức năng dự định.
        \begin{itemize}
            \item \textbf{Ví dụ}
            \begin{itemize}
                \item Sử dụng các chức năng đặc thù của OS $\rightarrow$ Ngôn ngữ C
                \item Hợp tác với cơ sở dữ liệu $\rightarrow$ Java hoặc Python
            \end{itemize}
        \end{itemize}
        \item[\Large $\square$] Xem xét bảo trì trong tương lai, tích cực tạo tài liệu như đặc tả chương trình và sơ đồ luồng.
    \end{itemize}
\end{frame}

\begin{frame}{(4) Kiểm Thử}
    \begin{itemize}
        \item[\Large $\square$] Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)
        \item[\Large $\square$] Phân Tích Yêu Cầu
        \item[\Large $\square$] Thiết Kế
        \item[\Large $\square$] Triển Khai (Lập Trình)
        \item[\Large $\square$] \alert{Kiểm Thử}
        \item[\Large $\square$] Vận Hành \& Bảo Trì
    \end{itemize}
\end{frame}

\begin{frame}{(4) Kiểm Thử}
    \begin{block}{Sản Phẩm Bàn Giao: Đặc Tả Kiểm Thử, Báo Cáo Kết Quả Kiểm Thử}
        Dựa trên các đặc tả khác nhau, kiểm tra xem phần mềm có hoạt động đúng không.
    \end{block}

    \begin{itemize}
        \item Chức năng (module) có phù hợp với đặc tả chương trình không? $\rightarrow$ \textbf{Kiểm Thử Đơn Vị}
        \item Hệ thống con có phù hợp với đặc tả bên trong không? $\rightarrow$ \textbf{Kiểm Thử Tích Hợp}
        \item Hệ thống có phù hợp với đặc tả bên ngoài không? $\rightarrow$ \textbf{Kiểm Thử Hệ Thống}
        \item Hệ thống có phù hợp với đặc tả yêu cầu không? $\rightarrow$ \textbf{Kiểm Thử Chấp Nhận}
    \end{itemize}
\end{frame}

\begin{frame}{(4) Kiểm Thử}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Kiểm Thử Đơn Vị}
        \begin{itemize}
            \item Một thành phần đơn lẻ (chức năng, v.v.) có hoạt động đúng không?
        \end{itemize}
        \item[\Large $\square$] \textbf{Kiểm Thử Tích Hợp}
        \begin{itemize}
            \item Một số thành phần được kết nối (ví dụ: một chức năng gọi chức năng khác) có hoạt động đúng không?
        \end{itemize}
        \item[\Large $\square$] \textbf{Kiểm Thử Hệ Thống}
        \begin{itemize}
            \item Hệ thống có hoạt động như được thiết kế và đặc tả không?
        \end{itemize}
        \item[\Large $\square$] \textbf{Kiểm Thử Chấp Nhận}
        \begin{itemize}
            \item Hệ thống có hoạt động theo cách thỏa mãn khách hàng không?
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{Nghỉ 10 phút}
    \begin{center}
        \Huge 10分休憩
    \end{center}
\end{frame}

\begin{frame}{(5) Vận Hành \& Bảo Trì}
    \begin{itemize}
        \item[\Large $\square$] Lập Kế Hoạch Phát Triển (Kế Hoạch Tổng Thể)
        \item[\Large $\square$] Phân Tích Yêu Cầu
        \item[\Large $\square$] Thiết Kế
        \item[\Large $\square$] Triển Khai (Lập Trình)
        \item[\Large $\square$] Kiểm Thử
        \item[\Large $\square$] \alert{Vận Hành \& Bảo Trì}
    \end{itemize}
\end{frame}

\begin{frame}{(5) Vận Hành \& Bảo Trì}
    \begin{block}{Sản Phẩm Bàn Giao: Các Hướng Dẫn Khác Nhau, Báo Cáo Sự Cố/Xử Lý}
        Vận hành bởi người dùng \& Bảo trì để đáp ứng việc phát hiện lỗi hoặc yêu cầu sửa đổi.
    \end{block}

    \begin{itemize}
        \item[\Large $\square$] Tạo hướng dẫn vận hành/bảo trì trước khi vận hành.
        \item[\Large $\square$] Nếu xảy ra lỗi, ghi lại và lưu giữ báo cáo và cách xử lý.
    \end{itemize}
\end{frame}

\begin{frame}{(Bổ Sung) Phân Biệt Các Thuật Ngữ}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Lỗi, bug, trục trặc (failure)}
        \begin{itemize}
            \item Hiện tượng phần mềm không hoạt động đúng.
        \end{itemize}
        \item[\Large $\square$] \textbf{Khiếm khuyết, lỗi (defect, fault)}
        \begin{itemize}
            \item Nguyên nhân trực tiếp của lỗi.
        \end{itemize}
        \item[\Large $\square$] \textbf{Sai lầm (error)}
        \begin{itemize}
            \item Sai lầm đã tạo ra khiếm khuyết.
        \end{itemize}
    \end{itemize}
    \begin{center}
        \begin{tikzpicture}
            \node[rectangle, draw] (bug) {Thuật ngữ "bug" là thuật ngữ mơ hồ bao gồm cả ba điều này.};
        \end{tikzpicture}
    \end{center}
\end{frame}

\begin{frame}{【Ví Dụ】Hệ Thống Thông Báo Bảng Tin (5) Vận Hành \& Bảo Trì}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Để họ thực sự sử dụng nó}
        \begin{itemize}
            \item Báo cáo bất kỳ lỗi nào.
            \item Đưa ra bất kỳ yêu cầu nào.
            \begin{itemize}
                \item Sử dụng bảng tin điện tử (BBS) và danh sách gửi thư.
            \end{itemize}
        \end{itemize}
        \item[\Large $\square$] \textbf{Trong bảo trì}
        \begin{itemize}
            \item Cũng có các hệ thống theo dõi lỗi như Bugzilla và JIRA.
            \item Sửa lỗi.
            \item Đáp ứng yêu cầu.
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Bài Tập 2】Trả lời quy trình tương ứng}
    \begin{block}{Đối với mỗi nhiệm vụ sau, xác định quy trình tương ứng (Phân Tích Yêu Cầu, Thiết Kế Bên Ngoài, Thiết Kế Bên Trong, Triển Khai, Kiểm Thử, Vận Hành \& Bảo Trì). Giả sử "Hệ Thống Kiểm Tra Tình Trạng Đông Đúc Căng Tin".}
    \begin{enumerate}
        \item Dữ liệu tình trạng đông đúc được quyết định là một struct gồm thời gian đo (ngày, giờ, phút) và tỷ lệ sử dụng ghế (\%), và được ghi vào file với khoảng thời gian 3 phút.
        \item Trước khi vận hành toàn diện, chúng tôi đã thực hiện truy cập cho 1000 người đồng thời và kiểm tra hành vi bằng cách truy cập ngoài giờ làm việc.
        \item Cấu hình hệ thống được chia thành ba phần để phát triển: (1) phần quản lý dữ liệu tình trạng đông đúc, (2) phần giao diện người dùng, và (3) phần điều khiển (controller).
        \item Chúng tôi đã tiến hành khảo sát người dùng và tổ chức các vấn đề cần cải thiện.
    \end{enumerate}
    \end{block}
\end{frame}

\begin{frame}{Mô Hình Quy Trình}
    \begin{itemize}
        \item[\Large $\square$] Quy trình phát triển phần mềm (về cơ bản, 5 quy trình sau)
        \begin{itemize}
            \item[\Large $\square$] Phân Tích Yêu Cầu
            \item[\Large $\square$] Thiết Kế
            \item[\Large $\square$] Triển Khai (Lập Trình)
            \item[\Large $\square$] Kiểm Thử
            \item[\Large $\square$] Vận Hành \& Bảo Trì
        \end{itemize}
        \item Mô hình cho thấy cách tiến hành các quy trình này được gọi là \alert{mô hình quy trình}.
    \end{itemize}
\end{frame}

\begin{frame}{Các Ví Dụ Đại Diện của Mô Hình Quy Trình}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Mô Hình Thác Nước (Waterfall)}
        \begin{itemize}
            \item (Cũng như một trong các biến thể của nó) \textbf{Mô Hình V}
        \end{itemize}
        \item[\Large $\square$] \textbf{Tạo Mẫu Thử (Prototyping)}
        \begin{itemize}
            \item (Đây là phương pháp phát triển hơn là mô hình quy trình)
        \end{itemize}
        \item[\Large $\square$] \textbf{Mô Hình Xoắn Ốc (Spiral)}
    \end{itemize}
\end{frame}

\begin{frame}{Mô Hình Thác Nước}
    \frametitle{Tiến hành tuần tự từ phân tích yêu cầu đến vận hành và bảo trì}

    \begin{tikzpicture}[node distance=1cm, auto]
        \node (analysis) [block] {Phân Tích Yêu Cầu};
        \node (ext_design) [block, below of=analysis, xshift=1.5cm] {Thiết Kế Bên Ngoài};
        \node (int_design) [block, below of=ext_design, xshift=1.5cm] {Thiết Kế Bên Trong};
        \node (impl) [block, below of=int_design, xshift=1.5cm] {Triển Khai (Lập Trình)};
        \node (test) [block, below of=impl, xshift=1.5cm] {Kiểm Thử};
        \node (maint) [block, below of=test, xshift=1.5cm] {Vận Hành/Bảo Trì};

        \path [line, bend left] (analysis) to (ext_design);
        \path [line, bend left] (ext_design) to (int_design);
        \path [line, bend left] (int_design) to (impl);
        \path [line, bend left] (impl) to (test);
        \path [line, bend left] (test) to (maint);

        \node[text width=10em, left of=int_design, xshift=-3cm] {Như nước chảy từ nơi cao xuống nơi thấp};
        \node[below of=impl, yshift=-1.5cm] {※Waterfall = 滝};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Mô Hình Thác Nước: Đặc Điểm}
    \begin{itemize}
        \item[\Large $\square$] Sau khi "Phân Tích Yêu Cầu" hoàn thành, tiến hành "Thiết Kế Bên Ngoài" tiếp theo, v.v., hoàn thành từng giai đoạn theo thứ tự.
        \begin{itemize}
            \item Dễ nắm bắt tiến độ của dự án.
            \item Phân công lao động rõ ràng.
            \item Đây là mô hình "tiêu chuẩn" truyền thống và dễ dạy.
            \begin{itemize}
                \item (※ Tài liệu đầu tiên được xuất bản năm 1970)
            \end{itemize}
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Ngay cả ngày nay, nó thường được áp dụng trong các dự án quy mô lớn đòi hỏi số lượng lớn nhân sự.
    \end{block}
\end{frame}

\begin{frame}{Mô Hình Thác Nước: Vấn Đề (1)}
    \begin{itemize}
        \item[\Large $\square$] Phân tích yêu cầu không phải lúc nào cũng được hoàn thành "hoàn toàn".
        \begin{itemize}
            \item Lý tưởng nhất, tất cả yêu cầu được đưa ra, phân tích, và đặc tả yêu cầu được hoàn thành.
            \item Tuy nhiên, trong thực tế, điều này khó khăn (đôi khi không nhận ra).
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{Có khả năng cao chuyển sang thiết kế với phân tích yêu cầu chưa hoàn chỉnh, tiền đề của mô hình thác nước bị đảo lộn, và có thể xảy ra nhầm lẫn.}
        Đặc tả thay đổi sau khi chương trình hoàn thành, v.v.
    \end{alertblock}
\end{frame}

\begin{frame}{Mô Hình Thác Nước: Vấn Đề (2)}
    \begin{itemize}
        \item[\Large $\square$] Sự chậm trễ trong quy trình thượng nguồn ảnh hưởng trực tiếp đến hạ nguồn.
        \begin{itemize}
            \item Ví dụ, nếu việc hoàn thành đặc tả yêu cầu bị trễ một tuần $\rightarrow$ thiết kế bên ngoài sẽ bắt đầu sau đó, nên tất nhiên sẽ bị trễ một tuần.
            \item Giống như xếp hàng một dòng. Nếu người phía trước mất thời gian, người phía sau phải đợi.
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Vì chúng ta đợi việc hoàn thành quy trình thượng nguồn, hạ nguồn bị ảnh hưởng bởi nó.
        Điều này cũng có thể ảnh hưởng đến thời hạn giao hàng và điều chỉnh lịch trình kỹ sư.
    \end{block}
\end{frame}

\begin{frame}{Mô Hình Thác Nước: Vấn Đề (3)}
    \begin{itemize}
        \item[\Large $\square$] Không phù hợp cho việc sửa chữa và làm lại quay trở lại thượng nguồn.
        \begin{itemize}
            \item Tiền đề là "quy trình thượng nguồn đã hoàn thành" tại mỗi thời điểm.
            \item Tuy nhiên, trong thực tế, có nhiều trường hợp "quay trở lại thượng nguồn để làm lại" (thử và sai là không thể tránh khỏi nếu bạn đang tạo hệ thống lần đầu).
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{Có thể trở thành mô hình không phù hợp với phát triển phần mềm thực tế $\rightarrow$ yếu tố tăng chi phí.}
        Ví dụ, ở giai đoạn "triển khai", nhóm "phân tích yêu cầu" có thể đã được giải tán hoặc đang làm việc trên dự án khác. Nếu được thuê ngoài, có thể cần hợp đồng bổ sung.
    \end{alertblock}
\end{frame}

\begin{frame}{Mô Hình Thác Nước: Vấn Đề (4)}
    \begin{itemize}
        \item[\Large $\square$] Kiểm thử không được thực hiện cho đến nửa cuối.
        \begin{itemize}
            \item Kiểm thử được thực hiện lần đầu tiên sau khi "triển khai" hoàn thành.
            \item Nếu phát hiện lỗi trong kiểm thử, "làm lại" tất nhiên là cần thiết, dẫn đến vấn đề (3) đã đề cập trước đó.
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Tập trung vào "xác nhận" hoạt động bình thường sau khi tạo, việc tìm thấy lỗi và phải làm lại lớn không phải là chủ đạo của mô hình.
        Mô hình dựa trên ý tưởng rằng việc phân tích yêu cầu và thiết kế có đúng hay không được xác nhận và kiểm chứng lần đầu tiên sau khi triển khai kết thúc. Ý tưởng cơ bản là tại mỗi thời điểm, quy trình thượng nguồn đang diễn ra tốt.
    \end{block}
\end{frame}

\begin{frame}[fragile]{Mô Hình V}
    \begin{itemize}
        \item[\Large $\square$] Bản chất giống như mô hình thác nước.
        \item[\Large $\square$] Quy trình kiểm thử được chi tiết hóa và liên kết với quy trình thượng nguồn.
    \end{itemize}

    \centering
    \begin{tikzpicture}[node distance=1.1cm and 0.8cm, scale=0.8, transform shape]
        \node (analysis) [block] {Phân Tích Yêu Cầu};
        \node (acceptance) [block, right of=analysis, xshift=4cm] {Kiểm Thử Chấp Nhận/Vận Hành};
        \node (ext_design) [block, below of=analysis] {Thiết Kế Bên Ngoài};
        \node (system_test) [block, right of=ext_design, xshift=4cm] {Kiểm Thử Hệ Thống};
        \node (int_design) [block, below of=ext_design] {Thiết Kế Bên Trong};
        \node (unit_test) [block, right of=int_design, xshift=4cm] {Kiểm Thử Đơn Vị/Tích Hợp};
        \node (impl) [block, below of=int_design] {Triển Khai};

        \path[line, dashed] (analysis) -- (acceptance) node[midway, above, font=\tiny] {Xác Minh/Thẩm Định};
        \path[line, dashed] (ext_design) -- (system_test);
        \path[line, dashed] (int_design) -- (unit_test);

        \path[line] (analysis) -- (ext_design);
        \path[line] (ext_design) -- (int_design);
        \path[line] (int_design) -- (impl);
        \path[line] (impl) -- (unit_test);
        \path[line] (unit_test) -- (system_test);
        \path[line] (system_test) -- (acceptance);

        \node[text width=12em, below of=impl, yshift=0.5cm, font=\small] {Ngầm hiểu mức độ làm lại sẽ được yêu cầu.};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Tạo Mẫu Thử (Prototyping)}
    \begin{itemize}
        \item[\Large $\square$] Phương pháp tiến hành quy trình phát triển trong khi xác nhận yêu cầu bằng cách tạo mẫu thử.
        \begin{itemize}
            \item Mẫu thử có thể là mô hình để xác nhận.
            \item Được sử dụng để xác nhận và đánh giá đặc tả và thiết kế.
        \end{itemize}
    \end{itemize}

    \begin{tikzpicture}[node distance=2cm]
        \node (analysis) [block] {Phân Tích Yêu Cầu};
        \node (ext_design) [block, right of=analysis] {Thiết Kế Bên Ngoài};
        \node (int_design) [block, right of=ext_design] {Thiết Kế Bên Trong};
        \node (dots) [right of=int_design] {...};
        \node (prototype) [block, below of=ext_design] {Mẫu Thử};

        \path[line] (analysis) -> (ext_design);
        \path[line] (ext_design) -> (int_design);
        \path[line] (int_design) -> (dots);
        \path[line, <->] (analysis) -- (prototype);
        \path[line, <->] (ext_design) -- (prototype);

        \node[below of=prototype, yshift=-0.5cm] {Ví dụ, "Màn hình như thế này có ổn không?"};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Mô Hình Xoắn Ốc}
    \begin{itemize}
        \item[\Large $\square$] Mô hình bao gồm mô hình Thác nước và tạo mẫu thử.
        \item[\Large $\square$] Quy trình quản lý hướng rủi ro.
    \end{itemize}

    \begin{columns}
        \begin{column}{0.5\textwidth}
            % Placeholder for spiral and cone images
            \begin{center}
                [Hình ảnh xoắn ốc] \\
                [Hình ảnh hình nón]
            \end{center}
        \end{column}
        \begin{column}{0.5\textwidth}
            \vspace{2cm}
            ※ spiral = xoắn ốc \\
            Phát triển bằng cách lặp lại cùng một quy trình nhiều lần.
        \end{column}
    \end{columns}

    \begin{block}{Phù hợp cho các dự án quy mô lớn}
    \end{block}
\end{frame}

\begin{frame}{Mô Hình Xoắn Ốc: Chu Kỳ Cơ Bản}
    \begin{tikzpicture}[node distance=3cm, auto]
        \node (goal) [block, text width=12em] {\textbf{① Thiết Lập Mục Tiêu} \\ Đặt ra những gì bạn muốn tạo (mục tiêu) trong quy trình đó. \\ Xem xét các phương tiện phù hợp với mục tiêu (càng nhiều càng tốt).};
        \node (eval) [block, right of=goal, xshift=2cm, text width=12em] {\textbf{② Đánh Giá} \\ Đánh giá ưu điểm và rủi ro của mỗi phương tiện. \\ Xác nhận bằng mẫu thử.};
        \node (create) [block, below of=eval, text width=12em] {\textbf{③ Tạo/Thực Hiện} \\ Thực sự tạo tài liệu/hệ thống. \\ Thực hiện kiểm thử.};
        \node (plan) [block, left of=create, xshift=-2cm, text width=12em] {\textbf{④ Kế Hoạch Tiếp Theo} \\ Lập kế hoạch quy trình sẽ được thực hiện trong lần lặp tiếp theo.};

        \path[line, ->] (goal) -- (eval);
        \path[line, ->] (eval) -- (create);
        \path[line, ->] (create) -- (plan);
        \path[line, ->, bend left] (plan) to (goal);
    \end{tikzpicture}
\end{frame}

\begin{frame}{Mô Hình Xoắn Ốc: Hình Ảnh Trực Quan}
    \begin{itemize}
        \item[\Large $\square$] Luồng tổng thể là mô hình thác nước, nhưng không tạo sản phẩm hoàn chỉnh cùng một lúc.
        \item[\Large $\square$] Giảm rủi ro thất bại bằng cách thực hiện xác nhận và đánh giá rủi ro trong mỗi quy trình.
    \end{itemize}
    \begin{itemize}
        \item Chu kỳ 1: Đặc Tả Yêu Cầu
        \item Chu kỳ 2: Thiết Kế Bên Ngoài
        \item Chu kỳ 3: Thiết Kế Bên Trong
        \item Chu kỳ 4: Triển Khai/Kiểm Thử
    \end{itemize}
\end{frame}

\begin{frame}{Các Phương Pháp/Mô Hình Khác ① Tạo Mẫu Thử Tiến Hóa}
    \frametitle{Dần dần phát triển mẫu thử thành sản phẩm hoàn chỉnh.}
    \begin{tikzpicture}[node distance=2.5cm]
        \node (v1) [shape=ellipse, draw, fill=blue!20] {Mẫu Thử Ver.1};
        \node (v2) [shape=ellipse, draw, fill=blue!30, right of=v1] {Mẫu Thử Ver.2};
        \node (v3) [shape=ellipse, draw, fill=blue!40, right of=v2] {Mẫu Thử Ver.3};

        \path[line, ->, bend left] (v1) to (v2);
        \path[line, ->, bend left] (v2) to (v3);

        \node[rectangle, draw, below of=v2, yshift=1cm] {Xác nhận đặc tả và bổ sung chức năng};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Các Phương Pháp/Mô Hình Khác ② Mô Hình Phát Triển Tăng Dần}
    \frametitle{Chia hệ thống mục tiêu thành nhiều phần và xây dựng theo thứ tự từ phần chính.}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{tikzpicture}[scale=0.7, transform shape]
                \node[draw, minimum height=3cm, minimum width=4cm, label=below:Phát triển phần cốt lõi] {};
                \node[draw, fill=red, minimum size=1cm] at (0,0) {};
            \end{tikzpicture}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{tikzpicture}[scale=0.7, transform shape]
                \node[draw, minimum height=3cm, minimum width=4cm, label=below:Phát triển phần ngoại vi (1)] {};
                \node[draw, fill=red, minimum size=1cm] at (0,0) {};
                \node[draw, fill=blue, minimum size=0.5cm] at (1,1) {};
            \end{tikzpicture}
        \end{column}
    \end{columns}
\end{frame}

\begin{frame}{Tóm Tắt}
    \begin{itemize}
        \item[\Large $\square$] Trước tiên, lập kế hoạch phát triển và phân tích yêu cầu là quan trọng.
        \item[\Large $\square$] Sau đó, tiến hành thiết kế bên ngoài (thiết kế hệ thống con), thiết kế bên trong (thiết kế module/chức năng), triển khai, kiểm thử, và vận hành/bảo trì.
        \item[\Large $\square$] \textbf{Mô Hình Quy Trình Phát Triển}
        \begin{itemize}
            \item \textbf{Mô Hình Thác Nước:} "Từ trên xuống dưới"
            \item \textbf{Tạo Mẫu Thử:} "Sử dụng mẫu thử làm cơ sở"
            \item \textbf{Mô Hình Xoắn Ốc:} "Lặp lại đánh giá và xác nhận"
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{Bài Tập Về Nhà}
    \begin{center}
        \Huge Trả lời "[03] quiz" \\ (trước thứ Sáu 23:59)
    \end{center}
    \vfill
    (Lưu ý: Điểm quiz của bạn sẽ là một phần của đánh giá cuối cùng)
\end{frame}

\begin{frame}{【Bài Tập 1】(Ví Dụ Trả Lời)}
    \frametitle{Về Hệ Thống Kiểm Tra Tình Trạng Đông Đúc Căng Tin}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Làm thế nào để kiểm tra?}
        \begin{itemize}
            \item Truy cập máy chủ bằng trình duyệt web?
            \item Có ứng dụng giao tiếp với máy chủ để hiển thị trạng thái?
        \end{itemize}
        \item[\Large $\square$] \textbf{Làm thế nào để biểu diễn trạng thái "đông đúc"?}
        \begin{itemize}
            \item Tình trạng ghế trống? (Hiển thị tỷ lệ sử dụng ghế hoặc bản đồ)
            \item Số người đã qua quầy thu ngân trong 20 phút qua?
        \end{itemize}
        \item[\Large $\square$] Có hạn chế nào về điện thoại thông minh có thể sử dụng không?
        \item[\Large $\square$] Có giới hạn trên về số người có thể sử dụng dịch vụ cùng lúc không?
    \end{itemize}
\end{frame}

\begin{frame}{【Bài Tập 2】(Đáp Án)}
    \frametitle{Trả lời quy trình tương ứng}
    \begin{enumerate}
        \item \textbf{【Thiết Kế Bên Trong】} Thiết kế hệ thống tham chiếu đến cấu trúc dữ liệu.
        \item \textbf{【Kiểm Thử】} Kiểm tra vận hành sơ bộ của hệ thống, thử các điều kiện tải cao và trường hợp sử dụng ngoại lệ.
        \item \textbf{【Thiết Kế Bên Ngoài】} Thiết kế hệ thống xác định cấu hình hệ thống con thô.
        \item \textbf{【Vận Hành \& Bảo Trì】} Các hoạt động nhằm phát triển và cải thiện hệ thống sau khi vận hành.
    \end{enumerate}
\end{frame}

\end{document}
