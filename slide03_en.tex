\documentclass{beamer}
\usepackage[utf8]{inputenc}
\usepackage[english]{babel}
\usepackage{tikz}
\usetikzlibrary{shapes, arrows}

% Define block styles
\tikzstyle{block} = [rectangle, draw, fill=blue!20, 
    text width=8em, text centered, rounded corners, minimum height=4em]
\tikzstyle{line} = [draw, -latex']

\title{Software Testing \\ [3] Software Development Process}
\author{Hirohisa AMAN \\ \texttt{<EMAIL>}}
\date{}

\begin{document}

\begin{frame}
    \titlepage
\end{frame}

\begin{frame}{Software Development Overview}
    \begin{tikzpicture}[node distance = 1cm, auto]
        % Place nodes
        \node [block] (planning) {Development Planning (Overall Plan)};
        \node [block, below of=planning] (analysis) {Requirements Analysis};
        \node [block, below of=analysis] (design) {Design};
        \node [block, below of=design] (implementation) {Implementation (Programming)};
        \node [block, below of=implementation] (testing) {Testing};
        \node [block, below of=testing] (maintenance) {Operation & Maintenance};
        
        % Draw edges
        \path [line] (planning) -- (analysis);
        \path [line] (analysis) -- (design);
        \path [line] (design) -- (implementation);
        \path [line] (implementation) -- (testing);
        \path [line] (testing) -- (maintenance);
        
        % Brace
        \draw[decorate,decoration={brace,amplitude=10pt,mirror},yshift=-0.2cm]
            (analysis.east) -- (testing.east) node[midway,right,xshift=0.5cm] {Development Process};
    \end{tikzpicture}
\end{frame}

\begin{frame}{(0) Development Planning}
    \begin{itemize}
        \item[\Large $\square$] \alert{Development Planning (Overall Plan)}
        \item[\Large $\square$] Requirements Analysis
        \item[\Large $\square$] Design
        \item[\Large $\square$] Implementation (Programming)
        \item[\Large $\square$] Testing
        \item[\Large $\square$] Operation & Maintenance
    \end{itemize}
\end{frame}

\begin{frame}{(0) Development Planning}
    \centering
    \begin{tikzpicture}[node distance=1.5cm]
        \node (setting) [block, text width=10em] {Setting: Define what to create and by when.};
        \node (estimation) [block, right of=setting, xshift=4cm, text width=7em] {Estimation: Estimate effort and cost.};
        \node (preparation) [block, below of=estimation, yshift=-1cm, text width=10em] {Preparation for Development Project:
            \begin{itemize}
                \item Staff assignment
                \item Budgetary measures
                \item Environmental arrangement, etc.
            \end{itemize}
        };
        \node (decision) [block, below of=setting, yshift=-1cm, text width=10em] {Decide development method.};
        \node (note) [rectangle, draw, below of=decision, yshift=-1.5cm, text width=15em] {Since it is not always possible to implement all "desired" functions within the period and budget by oneself, it is also necessary to decide how much to create.};
        
        \path[line] (setting) -- (estimation);
        \path[line] (estimation) -- (preparation);
        \path[line] (setting) -- (decision);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(0) Development Planning: Deciding on the Development Method}
    \begin{block}{Basic Policy}
        \begin{itemize}
            \item Develop from scratch?
            \item Base on an existing system?
        \end{itemize}
    \end{block}

    \begin{tikzpicture}[node distance=1.5cm, auto]
        \node (scale) [block, text width=20em] {Calculate the scale of development (e.g., how much code needs to be written?
)};
        \node (estimation) [block, below of=scale, text width=20em] {This affects the estimation of effort and cost.};
        \node (issues) [block, below of=estimation, text width=25em] {Other issues to consider: Securing human resources and team structure? Development environment? Budget?};
        
        \path [line] (scale) -- (estimation);
        \path [line] (estimation) -- (issues);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(1) Requirements Analysis}
    \begin{itemize}
        \item[\Large $\square$] Development Planning (Overall Plan)
        \item[\Large $\square$] \alert{Requirements Analysis}
        \item[\Large $\square$] Design
        \item[\Large $\square$] Implementation (Programming)
        \item[\Large $\square$] Testing
        \item[\Large $\square$] Operation & Maintenance
    \end{itemize}
    \vfill
    \begin{center}
        \huge \alert{Start of the development process}
    \end{center}
\end{frame}

\begin{frame}{(1) Requirements Analysis}
    \begin{block}{Deliverable: Requirements Specification}
        Clarify the content to be realized with the software.
    \end{block}
    
    \begin{itemize}
        \item[\Large $\square$] \textbf{Business Analysis}
        \begin{itemize}
            \item Analyze and organize the business operations to be realized by the software.
        \end{itemize}
        \item[\Large $\square$] \textbf{Description of Requirements}
        \begin{itemize}
            \item Describe accurately, without ambiguity.
            \item Also, consider feasibility from the perspectives of function, cost, and delivery date.
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Example】Bulletin Board Notification System (1) Requirements Analysis}
    \begin{itemize}
        \item[\Large $\square$] I want a system that notifies me on my smartphone when a relevant notice is posted.
        \begin{itemize}
            \item Real-time or summarized daily?
            \item In what form should the notification be sent?
            \item How to register/change the notification destination?
            \begin{itemize}
                \item What about anti-spoofing measures and security protection?
            \end{itemize}
            \item In what format should the notice content be entered?
            \item What are the conditions for a "relevant notice" in the first place?
            \begin{itemize}
                \item Is integration with the student database necessary?
            \end{itemize}
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Exercise 1】Consider the necessary study items for requirements analysis}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Request from the customer:}
        \begin{itemize}
            \item "I want you to create a system that allows me to check the congestion status of the cafeteria on my smartphone."
        \end{itemize}
        \item[\Large $\square$] \textbf{Consider and list the items that should be confirmed (or studied) with the customer when creating the requirements specification.}
    \end{itemize}
\end{frame}

\begin{frame}{(2) Design}
    \begin{itemize}
        \item[\Large $\square$] Development Planning (Overall Plan)
        \item[\Large $\square$] Requirements Analysis
        \item[\Large $\square$] \alert{Design}
        \item[\Large $\square$] Implementation (Programming)
        \item[\Large $\square$] Testing
        \item[\Large $\square$] Operation & Maintenance
    \end{itemize}
\end{frame}

\begin{frame}{(2-1) External Design}
    \begin{block}{Deliverable: External Specification}
        Divide the target system into subsystems and design the specifications of each subsystem as seen from the outside.
    \end{block}

    \begin{tikzpicture}[node distance=2cm]
        \node (user) [shape=rectangle, text width=6em, align=center] {User or External System};
        \node (system) [shape=ellipse, draw, fill=blue!20, text width=5em, align=center, right of=user, xshift=1cm] {Overall System};
        \node (subsystem) [shape=ellipse, draw, fill=orange!30, below of=user, xshift=1cm] {Subsystem};

        \node (q1) [above of=system, yshift=0.5cm] {What functions will it have?};
        \node (q2) [right of=system, xshift=0.5cm, align=center] {What kind of \\ interface will it have?};
        \node (q3) [below of=q2, align=center] {How will data and \\ control be exchanged?};
        \node (q4) [left of=subsystem, xshift=-2cm] {How will it be operated?};
        
        \path[line] (user) -- (system);
        \path[line] (system) -- (subsystem);
        \path[line] (subsystem) -- (user);
    \end{tikzpicture}
\end{frame}

\begin{frame}{(2-2) Internal Design}
    \begin{block}{Deliverable: Internal Specification}
        Design the program specifications in detail for each external design.
    \end{block}
    
    \begin{tikzpicture}[node distance=1.5cm]
        \node(subsystem) [shape=ellipse, draw, fill=orange!30] {Subsystem};
        \node(funcA) [block, right of=subsystem, xshift=2cm] {Function A};
        \node(funcB) [block, below of=funcA, yshift=-0.5cm, xshift=-1.5cm] {Function B};
        \node(funcC) [block, right of=funcB, xshift=1cm] {Function C};
        \node(funcD) [block, below of=funcB] {Function D};
        \node(funcF) [block, right of=funcD, xshift=1cm] {Function F};
        
        \path[line] (subsystem) -| (funcA);
        \path[line] (funcA) -- (funcB);
        \path[line] (funcA) -- (funcC);
        \path[line] (funcB) -- (funcD);
        \path[line] (funcC) -- (funcF);
        
        \node[rectangle, draw, right of=funcA, xshift=2.5cm, text width=10em, font=\scriptsize] {
            \begin{itemize}
                \item Program structure (job allocation as functions, etc.)
                \item Specifications of each function (I/O, functionality, etc.)
                \item Data structure and algorithm
            \end{itemize}
        };
        
        \node[below of=funcF, yshift=-1cm, text width=15em, align=center] {\alert{Design up to one step before programming}};
        \node[rectangle, draw, below of=subsystem, yshift=-2.5cm, text width=15em] {※ "Function" is not accurate, the concept is "Module"};

    \end{tikzpicture}
\end{frame}

\begin{frame}{【Example】Bulletin Board Notification System (2-1) External Design}
    \begin{tikzpicture}[node distance=2.5cm, auto,
        actor/.style={
          shape=rectangle,
          path picture={
            \node at (path picture bounding box.center){
                \begin{tikzpicture}
                    \draw (0,0.3) circle (0.2);
                    \draw (0,0.1) -- (0,-0.4);
                    \draw (-0.3,-0.1) -- (0.3,-0.1);
                    \draw (0,-0.4) -- (-0.3,-0.7);
                    \draw (0,-0.4) -- (0.3,-0.7);
                \end{tikzpicture}
            };
          }
        }]
        
        \node (user) [actor, label=below:User] {};
        \node (data_input) [actor, right of=user, xshift=6cm, label=below:Data Input Person] {};
        
        \node (content_mgmt) [block, above right of=user, yshift=1cm] {Contents Management};
        \node (user_mgmt) [block, right of=content_mgmt] {User Management};
        \node (sending_mgmt) [block, below of=content_mgmt, yshift=-1cm] {Sending Management};
        
        \node (student_db) [shape=cylinder, draw, shape border rotate=90, aspect=0.2, below of=user, yshift=-1cm, label=below:Student Database] {};
        \node (user_db) [shape=cylinder, draw, shape border rotate=90, aspect=0.2, below of=data_input, yshift=-1cm, label=below:User Database] {};
        
        \path[line] (user) -- (sending_mgmt);
        \path[line] (user) -- (user_mgmt);
        \path[line] (data_input) -- (content_mgmt);
        \path[line] (sending_mgmt) -- (student_db);
        \path[line] (user_mgmt) -- (user_db);
        \path[line] (content_mgmt) -- (sending_mgmt);
        
        \node[below of=user_db, yshift=-1cm, text width=15em, align=center] {※ Databases are not people, but...};
    \end{tikzpicture}
\end{frame}

\begin{frame}{【Example】Bulletin Board Notification System (2-2) Internal Design}
    \frametitle{Sending Management Subsystem}
    
    \begin{tikzpicture}[node distance=1.5cm, auto]
        \node (main) [block] {Sending Management Main};
        \node (create_list) [block, below of=main, xshift=-3cm] {Create Recipient List};
        \node (create_content) [block, right of=create_list, xshift=1cm] {Create Content};
        \node (send_message) [block, right of=create_content, xshift=1cm] {Send Message};
        \node (search_db) [block, below of=create_list] {Search Student Database};
        
        \path[line] (main) -- (create_list);
        \path[line] (main) -- (create_content);
        \path[line] (main) -- (send_message);
        \path[line] (create_list) -- (search_db);
        
        \node[rectangle, draw, right of=send_message, xshift=2cm, text width=10em, align=left, font=\small] {
            What data structure for the recipient list? \\
            How to coordinate with the database? \\
            How to send the message?
        };
    \end{tikzpicture}
\end{frame}

\begin{frame}{(3) Implementation (Programming)}
    \begin{itemize}
        \item[\Large $\square$] Development Planning (Overall Plan)
        \item[\Large $\square$] Requirements Analysis
        \item[\Large $\square$] Design
        \item[\Large $\square$] \alert{Implementation (Programming)}
        \item[\Large $\square$] Testing
        \item[\Large $\square$] Operation & Maintenance
    \end{itemize}
\end{frame}

\begin{frame}{(3) Implementation (Programming)}
    \begin{block}{Deliverables: Program Specification, Source Code}
        Coding based on the internal specification.
    \end{block}
    
    \begin{itemize}
        \item[\Large $\square$] Needless to say, perform coding (programming) using a programming language.
        \item[\Large $\square$] Also create related documents such as program specifications and flowcharts.
    \end{itemize}
\end{frame}

\begin{frame}{【Example】Bulletin Board Notification System (3) Implementation (Programming)}
    \begin{itemize}
        \item[\Large $\square$] Use libraries and programming languages suitable for realizing the intended functions.
        \begin{itemize}
            \item \textbf{For example}
            \begin{itemize}
                \item Utilize OS-specific functions $\rightarrow$ C language
                \item Cooperate with database $\rightarrow$ Java or Python
            \end{itemize}
        \end{itemize}
        \item[\Large $\square$] Considering future maintenance, actively create documents such as program specifications and flowcharts.
    \end{itemize}
\end{frame}

\begin{frame}{(4) Testing}
    \begin{itemize}
        \item[\Large $\square$] Development Planning (Overall Plan)
        \item[\Large $\square$] Requirements Analysis
        \item[\Large $\square$] Design
        \item[\Large $\square$] Implementation (Programming)
        \item[\Large $\square$] \alert{Testing}
        \item[\Large $\square$] Operation & Maintenance
    \end{itemize}
\end{frame}

\begin{frame}{(4) Testing}
    \begin{block}{Deliverables: Test Specification, Test Result Report}
        Based on various specifications, test whether the software operates properly.
    \end{block}
    
    \begin{itemize}
        \item Is the function (module) consistent with the program specification? $\rightarrow$ \textbf{Unit Test}
        \item Is the subsystem consistent with the internal specification? $\rightarrow$ \textbf{Integration Test}
        \item Is the system consistent with the external specification? $\rightarrow$ \textbf{System Test}
        \item Is the system consistent with the requirements specification? $\rightarrow$ \textbf{Acceptance Test}
    \end{itemize}
\end{frame}

\begin{frame}{(4) Testing}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Unit Test}
        \begin{itemize}
            \item Does a single component (function, etc.) work correctly?
        \end{itemize}
        \item[\Large $\square$] \textbf{Integration Test}
        \begin{itemize}
            \item Do several connected components (e.g., a function calling another function) work correctly?
        \end{itemize}
        \item[\Large $\square$] \textbf{System Test}
        \begin{itemize}
            \item Does the system work as designed and specified?
        \end{itemize}
        \item[\Large $\square$] \textbf{Acceptance Test}
        \begin{itemize}
            \item Does the system work in a way that satisfies the customer?
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{10-minutes rest break}
    \begin{center}
        \Huge 10分休憩
    \end{center}
\end{frame}

\begin{frame}{(5) Operation & Maintenance}
    \begin{itemize}
        \item[\Large $\square$] Development Planning (Overall Plan)
        \item[\Large $\square$] Requirements Analysis
        \item[\Large $\square$] Design
        \item[\Large $\square$] Implementation (Programming)
        \item[\Large $\square$] Testing
        \item[\Large $\square$] \alert{Operation & Maintenance}
    \end{itemize}
\end{frame}

\begin{frame}{(5) Operation & Maintenance}
    \begin{block}{Deliverables: Various Manuals, Trouble/Correspondence Report}
        Operation by the user & Maintenance in response to bug discovery or modification requests.
    \end{block}
    
    \begin{itemize}
        \item[\Large $\square$] Create operation/maintenance manuals prior to operation.
        \item[\Large $\square$] If a failure occurs, record and retain the report and correspondence.
    \end{itemize}
\end{frame}

\begin{frame}{(Supplement) Differentiating Terms}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Failure, bug, malfunction (failure)}
        \begin{itemize}
            \item A phenomenon in which software does not operate properly.
        \end{itemize}
        \item[\Large $\square$] \textbf{Defect, fault}
        \begin{itemize}
            \item The direct cause of a failure.
        \end{itemize}
        \item[\Large $\square$] \textbf{Error}
        \begin{itemize}
            \item The mistake that created the defect.
        \end{itemize}
    \end{itemize}
    \begin{center}
        \begin{tikzpicture}
            \node[rectangle, draw] (bug) {The term "bug" is a vague term that includes these three.};
        \end{tikzpicture}
    \end{center}
\end{frame}

\begin{frame}{【Example】Bulletin Board Notification System (5) Operation & Maintenance}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Have them actually use it}
        \begin{itemize}
            \item Report any bugs.
            \item Make any requests.
            \begin{itemize}
                \item Utilize electronic bulletin boards (BBS) and mailing lists.
            \end{itemize}
        \end{itemize}
        \item[\Large $\square$] \textbf{In maintenance}
        \begin{itemize}
            \item There are also bug tracking systems like Bugzilla and JIRA.
            \item Correct bugs.
            \item Respond to requests.
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{【Exercise 2】Answer the corresponding process}
    \begin{block}{For each of the following tasks, identify the corresponding process (Requirements Analysis, External Design, Internal Design, Implementation, Testing, Operation & Maintenance). Assume a "Cafeteria Congestion Check System".}
    \begin{enumerate}
        \item The congestion data was decided to be a struct of measurement time (day, hour, minute) and seat usage rate (\%), and to be recorded to a file at 3-minute intervals.
        \item Before full-scale operation, we executed access for 1000 people simultaneously and checked the behavior by accessing outside business hours.
        \item The system configuration was divided into three parts for development: (1) congestion data management part, (2) user interface part, and (3) control (controller) part.
        \item We conducted a user survey and organized the issues to be improved.
    \end{enumerate}
    \end{block}
\end{frame}

\begin{frame}{Process Model}
    \begin{itemize}
        \item[\Large $\square$] The software development process (basically, the following 5 processes)
        \begin{itemize}
            \item[\Large $\square$] Requirements Analysis
            \item[\Large $\square$] Design
            \item[\Large $\square$] Implementation (Programming)
            \item[\Large $\square$] Testing
            \item[\Large $\square$] Operation & Maintenance
        \end{itemize}
        \item A model that shows how to proceed with these is called a \alert{process model}.
    \end{itemize}
\end{frame}

\begin{frame}{Representative Examples of Process Models}
    \begin{itemize}
        \item[\Large $\square$] \textbf{Waterfall Model}
        \begin{itemize}
            \item (Also, as one of its variations) \textbf{V-Model}
        \end{itemize}
        \item[\Large $\square$] \textbf{Prototyping}
        \begin{itemize}
            \item (This is more of a development method than a process model itself)
        \end{itemize}
        \item[\Large $\square$] \textbf{Spiral Model}
    \end{itemize}
\end{frame}

\begin{frame}{Waterfall Model}
    \frametitle{Proceed sequentially from requirements analysis to operation and maintenance}
    
    \begin{tikzpicture}[node distance=1cm, auto]
        \node (analysis) [block] {Requirements Analysis};
        \node (ext_design) [block, below of=analysis, xshift=1.5cm] {External Design};
        \node (int_design) [block, below of=ext_design, xshift=1.5cm] {Internal Design};
        \node (impl) [block, below of=int_design, xshift=1.5cm] {Implementation (Programming)};
        \node (test) [block, below of=impl, xshift=1.5cm] {Test};
        \node (maint) [block, below of=test, xshift=1.5cm] {Operation/Maintenance};
        
        \path [line, bend left] (analysis) to (ext_design);
        \path [line, bend left] (ext_design) to (int_design);
        \path [line, bend left] (int_design) to (impl);
        \path [line, bend left] (impl) to (test);
        \path [line, bend left] (test) to (maint);
        
        \node[text width=10em, left of=int_design, xshift=-3cm] {Like water flowing from a higher place to a lower place};
        \node[below of=impl, yshift=-1.5cm] {※Waterfall = 滝};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Waterfall Model: Features}
    \begin{itemize}
        \item[\Large $\square$] After "Requirements Analysis" is completed, proceed to the next "External Design", and so on, completing each stage in order.
        \begin{itemize}
            \item Easy to grasp the progress of the project.
            \item The division of labor is clear.
            \item It is a traditional "standard" model and easy to teach.
            \begin{itemize}
                \item (※ The first literature was published in 1970)
            \end{itemize}
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Even today, it is often adopted in large-scale projects that require a large number of personnel.
    \end{block}
\end{frame}

\begin{frame}{Waterfall Model: Problems (1)}
    \begin{itemize}
        \item[\Large $\square$] Requirements analysis is not always "completely" completed.
        \begin{itemize}
            \item Ideally, all requirements are put out, analyzed, and the requirements specification is completed.
            \item However, in reality, it is difficult (sometimes things are not noticed).
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{There is a high possibility of moving to the design with incomplete requirements analysis, the premise of the waterfall model is overturned, and confusion may arise.}
        The specifications change after the program is completed, etc.
    \end{alertblock}
\end{frame}

\begin{frame}{Waterfall Model: Problems (2)}
    \begin{itemize}
        \item[\Large $\square$] Delays in the upstream process directly affect the downstream.
        \begin{itemize}
            \item For example, if the completion of the requirements specification is delayed by one week $\rightarrow$ the external design will start after that, so of course, it will be delayed by one week.
            \item It's like waiting in a single line. If the person in front takes time, the person behind has to wait.
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Since we wait for the completion of the upstream process, the downstream is affected by it.
        This can also affect delivery deadlines and engineer schedule adjustments.
    \end{block}
\end{frame}

\begin{frame}{Waterfall Model: Problems (3)}
    \begin{itemize}
        \item[\Large $\square$] It is not suitable for corrections and rework that go back upstream.
        \begin{itemize}
            \item The premise is that "the upstream process is complete" at each point in time.
            \item However, in reality, there are many cases of "going back upstream to redo" (trial and error is inevitable if you are creating a system for the first time).
        \end{itemize}
    \end{itemize}
    \begin{alertblock}{It can become a model that does not suit actual software development $\rightarrow$ a factor in cost increase.}
        For example, at the "implementation" stage, the "requirements analysis" team may have already been disbanded or be working on another project. If it were outsourced, an additional contract might be necessary.
    \end{alertblock}
\end{frame}

\begin{frame}{Waterfall Model: Problems (4)}
    \begin{itemize}
        \item[\Large $\square$] Testing is not performed until the second half.
        \begin{itemize}
            \item Testing is performed for the first time after "implementation" is completed.
            \item If a bug is found in the test, "rework" is of course necessary, which leads to problem (3) mentioned earlier.
        \end{itemize}
    \end{itemize}
    \begin{block}{}
        Focusing on "confirming" normal operation after creation, finding a bug and having to do a major rework is not the mainstream of the model.
        The model is based on the idea that whether the requirements analysis and design were correct is confirmed and verified for the first time after the implementation is over. The underlying idea is that at each point in time, the upstream process is going well.
    \end{block}
\end{frame}

\begin{frame}[fragile]{V-Model}
    \begin{itemize}
        \item[\Large $\square$] The essence is the same as the waterfall model.
        \item[\Large $\square$] The testing process is detailed and linked to the upstream process.
    \end{itemize}
    
    \centering
    \begin{tikzpicture}[node distance=1.1cm and 0.8cm, scale=0.8, transform shape]
        \node (analysis) [block] {Requirements Analysis};
        \node (acceptance) [block, right of=analysis, xshift=4cm] {Acceptance/Op Test};
        \node (ext_design) [block, below of=analysis] {External Design};
        \node (system_test) [block, right of=ext_design, xshift=4cm] {System Test};
        \node (int_design) [block, below of=ext_design] {Internal Design};
        \node (unit_test) [block, right of=int_design, xshift=4cm] {Unit/Integration Test};
        \node (impl) [block, below of=int_design] {Implementation};
        
        \path[line, dashed] (analysis) -- (acceptance) node[midway, above, font=\tiny] {Verification/Validation};
        \path[line, dashed] (ext_design) -- (system_test);
        \path[line, dashed] (int_design) -- (unit_test);
        
        \path[line] (analysis) -- (ext_design);
        \path[line] (ext_design) -- (int_design);
        \path[line] (int_design) -- (impl);
        \path[line] (impl) -- (unit_test);
        \path[line] (unit_test) -- (system_test);
        \path[line] (system_test) -- (acceptance);
        
        \node[text width=12em, below of=impl, yshift=0.5cm, font=\small] {Implicitly means which level of rework will be required.};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Prototyping}
    \begin{itemize}
        \item[\Large $\square$] A method of proceeding with the development process while confirming requirements by creating a prototype.
        \begin{itemize}
            \item The prototype can be a mock-up for confirmation.
            \item Used for confirmation and evaluation of specifications and design.
        \end{itemize}
    \end{itemize}
    
    \begin{tikzpicture}[node distance=2cm]
        \node (analysis) [block] {Requirements Analysis};
        \node (ext_design) [block, right of=analysis] {External Design};
        \node (int_design) [block, right of=ext_design] {Internal Design};
        \node (dots) [right of=int_design] {...};
        \node (prototype) [block, below of=ext_design] {Prototype};
        
        \path[line] (analysis) -> (ext_design);
        \path[line] (ext_design) -> (int_design);
        \path[line] (int_design) -> (dots);
        \path[line, <->] (analysis) -- (prototype);
        \path[line, <->] (ext_design) -- (prototype);

        \node[below of=prototype, yshift=-0.5cm] {For example, "Is a screen like this okay?"};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Spiral Model}
    \begin{itemize}
        \item[\Large $\square$] A model that includes the Waterfall model and prototyping.
        \item[\Large $\square$] A risk-driven management process.
    \end{itemize}
    
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \includegraphics[width=\textwidth]{spiral.png} % Placeholder for spiral image
            \includegraphics[width=\textwidth]{cone.png} % Placeholder for cone image
        \end{column}
        \begin{column}{0.5\textwidth}
            \vspace{2cm}
            ※ spiral = らせん (rasen) \\
            Evolve by repeating the same process over and over again.
        \end{column}
    \end{columns}
    
    \begin{block}{Suited for large-scale projects}
    \end{block}
\end{frame}

\begin{frame}{Spiral Model: Basic Cycle}
    \begin{tikzpicture}[node distance=3cm, auto]
        \node (goal) [block, text width=12em] {\textbf{① Goal Setting} \\ Set what you want to create (goal) in that process. \\ Consider means that match the goal (as many as possible).};
        \node (eval) [block, right of=goal, xshift=2cm, text width=12em] {\textbf{② Evaluation} \\ Evaluate the merits and risks of each means. \\ Confirmation by prototype.};
        \node (create) [block, below of=eval, text width=12em] {\textbf{③ Creation/Execution} \\ Actually create documents/systems. \\ Execute tests.};
        \node (plan) [block, left of=create, xshift=-2cm, text width=12em] {\textbf{④ Next Plan} \\ Plan the process to be performed in the next iteration.};
        
        \path[line, ->] (goal) -- (eval);
        \path[line, ->] (eval) -- (create);
        \path[line, ->] (create) -- (plan);
        \path[line, ->, bend left] (plan) to (goal);
    \end{tikzpicture}
\end{frame}

\begin{frame}{Spiral Model: Intuitive Image}
    \begin{itemize}
        \item[\Large $\square$] The overall flow is a waterfall model, but it does not create a finished product at once.
        \item[\Large $\square$] Reduce the risk of failure by performing confirmation and risk assessment in each process.
    \end{itemize}
    \begin{itemize}
        \item Cycle 1: Requirements Specification
        \item Cycle 2: External Design
        \item Cycle 3: Internal Design
        \item Cycle 4: Implementation/Test
    \end{itemize}
\end{frame}

\begin{frame}{Other Methods/Models ① Evolutionary Prototyping}
    \frametitle{Gradually evolve the prototype into a finished product.}
    \begin{tikzpicture}[node distance=2.5cm]
        \node (v1) [shape=ellipse, draw, fill=blue!20] {Prototype Ver.1};
        \node (v2) [shape=ellipse, draw, fill=blue!30, right of=v1] {Prototype Ver.2};
        \node (v3) [shape=ellipse, draw, fill=blue!40, right of=v2] {Prototype Ver.3};
        
        \path[line, ->, bend left] (v1) to (v2);
        \path[line, ->, bend left] (v2) to (v3);
        
        \node[rectangle, draw, below of=v2, yshift=1cm] {Confirmation of specifications and addition of functions};
    \end{tikzpicture}
\end{frame}

\begin{frame}{Other Methods/Models ② Incremental Development Model}
    \frametitle{Divide the target system into several parts and build them up in order from the main part.}
    \begin{columns}
        \begin{column}{0.5\textwidth}
            \begin{tikzpicture}[scale=0.7, transform shape]
                \node[draw, minimum height=3cm, minimum width=4cm, label=below:Develop core part] {};
                \node[draw, fill=red, minimum size=1cm] at (0,0) {};
            \end{tikzpicture}
        \end{column}
        \begin{column}{0.5\textwidth}
            \begin{tikzpicture}[scale=0.7, transform shape]
                \node[draw, minimum height=3cm, minimum width=4cm, label=below:Develop peripheral part (1)] {};
                \node[draw, fill=red, minimum size=1cm] at (0,0) {};
                \node[draw, fill=blue, minimum size=0.5cm] at (1,1) {};
            \end{tikzpicture}
        \end{column}
    \end{columns}
\end{frame}

\begin{frame}{Summary}
    \begin{itemize}
        \item[\Large $\square$] First, development planning and requirements analysis are important.
        \item[\Large $\square$] Then, proceed with external design (subsystem design), internal design (module/function design), implementation, testing, and operation/maintenance.
        \item[\Large $\square$] \textbf{Development Process Models}
        \begin{itemize}
            \item \textbf{Waterfall Model:} "From top to bottom"
            \item \textbf{Prototyping:} "Use a prototype as a base"
            \item \textbf{Spiral Model:} "Repeat evaluation and confirmation"
        \end{itemize}
    \end{itemize}
\end{frame}

\begin{frame}{Homework}
    \begin{center}
        \Huge Answer "[03] quiz" \\ (by this Friday 23:59)
    \end{center}
    \vfill
    (Note: Your quiz score will be a part of your final evaluation)
\end{frame}

\begin{frame}{【Exercise 1】(Answer Example)}
    \frametitle{About the Cafeteria Congestion Check System}
    \begin{itemize}
        \item[\Large $\square$] \textbf{How do you check it?}
        \begin{itemize}
            \item Access the server with a web browser?
            \item Have an app communicate with the server to display the status?
        \end{itemize}
        \item[\Large $\square$] \textbf{How do you represent the "congestion" status?}
        \begin{itemize}
            \item Seat availability? (Display seat occupancy rate or a map)
            \item Number of people who passed through the cash register in the last 20 minutes?
        \end{itemize}
        \item[\Large $\square$] Are there any restrictions on usable smartphones?
        \item[\Large $\square$] Is there an upper limit on the number of people who can use the service at the same time?
    \end{itemize}
\end{frame}

\begin{frame}{【Exercise 2】(Answer)}
    \frametitle{Answer the corresponding process}
    \begin{enumerate}
        \item \textbf{【Internal Design】} System design that refers to the data structure.
        \item \textbf{【Testing】} Preliminary operation check of the system, trying high load conditions and exceptional use cases.
        \item \textbf{【External Design】} System design that determines the rough subsystem configuration.
        \item \textbf{【Operation & Maintenance】} Activities aimed at the development and improvement of the system after operation.
    \end{enumerate}
\end{frame}

\end{document}